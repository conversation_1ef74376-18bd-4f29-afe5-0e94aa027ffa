import { useState, useContext } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "./sidebar";
import { Icon } from "@iconify/react";
import { AppContext } from "../Context/AppContext";
import secureLocalStorage from "react-secure-storage";
import LandingPage from "./LandingPage";


const Layout = () => {
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const { isMemeMode, memeTheme, memeThemes } = useContext(AppContext);
  const userData = secureLocalStorage.getItem("userData");

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  // Get current theme background
  const getThemeBackground = () => {
    if (!isMemeMode) return 'bg-[#fff5f8]';
    const currentTheme = memeThemes[memeTheme];
    return `bg-gradient-to-br ${currentTheme?.background || 'from-purple-400 via-pink-500 to-red-500'} animate-rainbow`;
  };

  // If user is not logged in, show the landing page
  if (!userData) {
    return <LandingPage />;
  }

  return (
    <div className={`flex h-screen relative transition-all duration-500 ${getThemeBackground()}`}>
      {/* Sidebar component */}
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />

      {/* Absolute Menu Icon only for smaller screens */}
      {!isSidebarOpen && (
        <button
          onClick={toggleSidebar}
          className={`lg:hidden absolute top-4 left-4 z-50 p-3 transition-all duration-300 ${
            isMemeMode
              ? 'text-white hover:text-yellow-300 animate-bounce'
              : 'text-black hover:text-gray-600'
          }`}
        >
          <Icon icon="mdi:menu" fontSize={30} />
        </button>
      )}

      <div className="flex-grow h-full p-3 overflow-y-auto">
        <Outlet />
      </div>

      {/* Meme Mode Floating Elements */}
      {isMemeMode && (
        <div className="fixed inset-0 pointer-events-none z-10">
          <div className="absolute top-20 left-10 text-4xl animate-bounce">🚀</div>
          <div className="absolute top-32 right-20 text-3xl animate-pulse">💎</div>
          <div className="absolute bottom-20 left-20 text-3xl animate-wiggle">🌙</div>
          <div className="absolute bottom-32 right-10 text-4xl animate-float">⭐</div>
          <div className="absolute top-1/2 left-1/4 text-2xl animate-pulse">🎉</div>
          {/* <div className="absolute top-1/3 right-1/3 text-2xl animate-bounce">🔥</div> */}
        </div>
      )}
    </div>
  );
};

export default Layout;