import React from "react";
import { useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react";

const Modal = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  if (!isOpen) return null;
  const handleSignIn = () => {
    navigate("signin");
  };

  const handleSignUp = () => {
    navigate("signup");
  };
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-80 relative">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
        >
          <Icon icon="mdi:close" fontSize={24} />
        </button>
        <h2 className="text-center text-lg font-semibold mb-4">Welcome!</h2>
        <p className="text-center text-sm text-gray-600 mb-6">
          Please sign in or sign up to continue.
        </p>
        <div className="flex flex-col gap-4">
          <button
            onClick={handleSignIn}
            className="bg-black text-white py-2 px-4 rounded-md hover:bg-gray-600 transition"
          >
            Sign In
          </button>
          <button
            onClick={handleSignUp}
            className="bg-black text-white py-2 px-4 rounded-md hover:bg-gray-600 transition"
          >
            Sign Up
          </button>
        </div>
      </div>
    </div>
  );
};

export default Modal;
