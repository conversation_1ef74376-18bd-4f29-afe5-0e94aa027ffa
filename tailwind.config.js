/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      animation: {
        fadeIn: "fadeIn 2s ease-in-out",
        bounce: "bounce 1s infinite",
        pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        wiggle: "wiggle 1s ease-in-out infinite",
        rainbow: "rainbow 3s linear infinite",
        float: "float 3s ease-in-out infinite",
        "fade-in-up": "fadeInUp 0.8s ease-out",
        "slide-in-left": "slideInLeft 0.6s ease-out",
        "slide-in-right": "slideInRight 0.6s ease-out",
        "glow": "glow 2s ease-in-out infinite alternate",
      },
      keyframes: {
        fadeInUp: {
          "0%": {
            opacity: "0",
            transform: "translateY(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        slideInLeft: {
          "0%": {
            opacity: "0",
            transform: "translateX(-30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        slideInRight: {
          "0%": {
            opacity: "0",
            transform: "translateX(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        glow: {
          "0%": {
            boxShadow: "0 0 20px rgba(147, 51, 234, 0.5)",
          },
          "100%": {
            boxShadow: "0 0 30px rgba(147, 51, 234, 0.8), 0 0 40px rgba(59, 130, 246, 0.5)",
          },
        },
        float: {
          "0%, 100%": {
            transform: "translateY(0px)",
          },
          "50%": {
            transform: "translateY(-20px)",
          },
        },
        wiggle: {
          "0%, 100%": {
            transform: "rotate(-3deg)",
          },
          "50%": {
            transform: "rotate(3deg)",
          },
        },
        rainbow: {
          "0%": {
            backgroundPosition: "0% 50%",
          },
          "50%": {
            backgroundPosition: "100% 50%",
          },
          "100%": {
            backgroundPosition: "0% 50%",
          },
        },
      },
      backgroundImage: {
        customGradient: "linear-gradient(135deg, #0085FF 0%, #9771EE 69.71%)",
        customGradient2:
          "linear-gradient(135deg, rgba(0, 133, 255, 0.20) 0%, rgba(151, 113, 238, 0.20) 69.71%)",
        memeGradient: "linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff)",
        memeBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      },
      boxShadow: {
        shadowBottom: "0px_4px_8px_0px_rgba(0,133,255,0.5)",
        profileShadow: "14px 17px 40px 4px rgba(112, 144, 176, 0.08)",
        customBlue: "0px 4px 16px 0px rgba(0, 72, 123, 0.25)",
        spaceSlider: "0px 0px 16px 0px rgba(0, 0, 0, 0.08)",
        connectCard:
          "-3.182px -3.182px 4.243px 0px #EEEEF8, 0px 16.971px 25.456px 0px rgba(0, 0, 0, 0.06), 0px 2.121px 6.364px 0px rgba(0, 0, 0, 0.04), 0px 0px 1.061px 0px rgba(0, 0, 0, 0.04)",
        connectionsCard:
          "-3.09px -3.09px 4.12px 0px rgba(0, 0, 0, 0.10), 0px 16.478px 24.718px 0px rgba(0, 0, 0, 0.06), 0px 2.06px 6.179px 0px rgba(0, 0, 0, 0.04), 0px 0px 1.03px 0px rgba(0, 0, 0, 0.04)",
      },
      fontFamily: {
        poppins: ["Poppins"],
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        wiggle: {
          "0%, 100%": { transform: "rotate(-3deg)" },
          "50%": { transform: "rotate(3deg)" },
        },
        rainbow: {
          "0%, 100%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
        "rotate-360": {
          "0%": { transform: "rotate(0deg)" },
          "100%": { transform: "rotate(360deg)" },
        },
      },
    },
  },
  plugins: [],
};
