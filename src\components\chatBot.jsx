import React from "react";

const data = [
  {
    id: 1,
    question: "What is web accessibility?",
    answer:
      "Web accessibility ensures that websites and digital tools are usable by people with disabilities, including those with visual, auditory, motor, or cognitive impairments.",
    userAvatar: "/assets/User Avatar.svg",
    aiAvatar: "/assets/AI Avatar.svg",
    editIcon: "/assets/edit.svg",
  },
  {
    id: 2,
    question: "Why is web accessibility important?",
    answer:
      "Web accessibility is important because it promotes inclusivity, providing equal access and opportunities for all users, regardless of their abilities.",
    userAvatar: "/assets/User Avatar.svg",
    aiAvatar: "/assets/AI Avatar.svg",
    editIcon: "/assets/edit.svg",
  },
];

const ChatBot = () => {
  return (
    <div className=" h-auto md:h-[calc(100vh-100px)]">
      <div className="p-6   flex space-y-6 flex-col items-center h-auto md:h-[calc(100vh-150px)] ">
        <div className="grid grid-cols-1 gap-6 place-items-center">
          {data.map((item) => (
            <div key={item.id} className="w-full max-w-2xl space-y-4">
              {/* Question Section */}
              <div className="grid grid-cols-[1fr_auto] gap-4 items-center">
                <div className="w-full p-2 border rounded-3xl">
                  <div className="flex items-center justify-between w-full">
                    <p className="text-sm w-full">{item.question}</p>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 bg-pink-100 rounded-full">
                        <img src={item.editIcon} alt="Edit" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="border rounded-full">
                  <img
                    src={item.userAvatar}
                    alt="User Avatar"
                    className="w-10"
                  />
                </div>
              </div>

              {/* Answer Section */}
              <div className="grid grid-cols-[auto_1fr] gap-4">
                <img src={item.aiAvatar} alt="AI Avatar" />
                <div className="p-2 bg-white rounded-2xl shadow-sm">
                  <p className="mt-2 text-sm text-gray-700">{item.answer}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <button className="mt-6 px-4 py-3  rounded-full border  flex items-center space-x-2">
          <img src="/assets/regenerate.svg" className="" />
          <span className="text-sm font-semibold ">Regenerate response</span>
        </button>
      </div>
      <div class="relative flex items-center w-full  max-w-3xl mx-auto ">
        <input
          type="text"
          class="w-full py-4 pl-20 pr-12  border bg-[#fffafb] rounded-full focus:outline-none focus:ring-2 focus:ring-pink-200"
          placeholder="Type your message here...."
        />
        <button class="absolute left-3  p-2 rounded-full">
          <img
            src="/assets/file-attach.svg"
            alt="Send"
            class="w-6  text-white"
          />
        </button>
        <button class="absolute right-3 bg-black p-2 rounded-full">
          <img src="/assets/send.svg" alt="Send" class="w-4 h-4 text-white" />
        </button>
      </div>
    </div>
  );
};

export default ChatBot;
