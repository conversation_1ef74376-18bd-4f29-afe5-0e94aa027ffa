import React, { useState, useEffect } from "react";
import axios from "axios";
import { Icon } from "@iconify/react";

// Sample news data for fallback
const getSampleNews = () => [
   {
    id: "1",
    title: "Bitcoin Surges Past $60,000 as Institutional Interest Grows",
    url: "https://example.com/news/1",
    imageurl: "https://images.cryptocompare.com/news/default.png",
    source: "CryptoNews",
    published_on: Date.now() / 1000 - 3600, // 1 hour ago
  },
  {
    id: "2",
    title: "Ethereum 2.0 Upgrade: What You Need to Know",
    url: "https://example.com/news/2",
    imageurl: "https://images.cryptocompare.com/news/default.png",
    source: "BlockchainReport",
    published_on: Date.now() / 1000 - 7200, // 2 hours ago
  },
  {
    id: "3",
    title: "Regulatory Developments: New Crypto Policies Announced",
    url: "https://example.com/news/3",
    imageurl: "https://images.cryptocompare.com/news/default.png",
    source: "CryptoInsider",
    published_on: Date.now() / 1000 - 10800, // 3 hours ago
  },
];

const TrendingNews = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchNews = async () => {
    try {
      setLoading(true);
      setError(null);
      // Using CryptoCompare News API - you can replace with your preferred API
      const response = await axios.get(
        "https://min-api.cryptocompare.com/data/v2/news/?lang=EN&categories=BTC,ETH,Cryptocurrency&excludeCategories=Sponsored&limit=3"
      );
      setNews(response.data.Data);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching crypto news:", err);
      setError("Failed to load news");
      setLoading(false);

      // Fallback to sample news if API fails
      setNews(getSampleNews());
    }
  };

  useEffect(() => {
    fetchNews();
  }, []);

  // Format the timestamp to a readable format
  const formatTime = (timestamp) => {
    const now = Date.now() / 1000;
    const diff = now - timestamp;

    if (diff < 60) return "Just now";
    if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
    if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`;
    return `${Math.floor(diff / 86400)}d ago`;
  };

  if (error && !news.length) {
    return (
      <div className="mt-6">
        <div className="flex justify-between items-center mb-2">
          <h4 className="font-medium">Trending Crypto News</h4>
          <button
            onClick={fetchNews}
            className="text-gray-500 hover:text-blue-500 transition-colors"
            title="Refresh news"
          >
            <Icon icon="mdi:refresh" fontSize={16} />
          </button>
        </div>
        <img className="mb-4" src="/assets/lineBreak.svg" alt="divider" />
        <div className="text-sm text-gray-500 py-2 flex items-center gap-2">
          <Icon icon="mdi:alert-circle-outline" fontSize={16} />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium">Trending Crypto News</h4>
        <button
          onClick={fetchNews}
          className="text-gray-500 hover:text-blue-500 transition-colors"
          title="Refresh news"
          disabled={loading}
        >
          <Icon icon={loading ? "eos-icons:loading" : "mdi:refresh"} fontSize={16} />
        </button>
      </div>
      <img className="mb-4" src="/assets/lineBreak.svg" alt="divider" />
      <ul className="space-y-4 text-sm overflow-y-auto max-h-60">
        {news.map((item) => (
          <li key={item.id} className="border-b pb-3 last:border-b-0">
            <a
              href={item.url}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-500 group"
            >
              <div className="flex items-start gap-2">
                <div className="flex-shrink-0 w-10 h-10 overflow-hidden rounded">
                  <img
                    src={item.imageurl || "https://images.cryptocompare.com/news/default.png"}
                    alt={item.source}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = "https://images.cryptocompare.com/news/default.png";
                    }}
                  />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium line-clamp-2 group-hover:text-blue-500 transition-colors">{item.title}</h5>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>{item.source}</span>
                    <span>{formatTime(item.published_on)}</span>
                  </div>
                </div>
              </div>
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TrendingNews;
