import React, { useContext, useEffect, useRef, useState } from "react";
import Header from "./header";
import secureLocalStorage from "react-secure-storage";
import Modal from "./modal";
import ReactMarkDown from "react-markdown";
import { AppContext } from "../Context/AppContext";
import InvestmentModal from "./userInfo";
import FeedbackModal from "./FeedbackModal";
import axios from "axios";

// Add confetti animation styles and other animations
const animationStyles = `
  @keyframes confetti {
    0% {
      transform: translateY(0) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(100vh) rotate(720deg);
      opacity: 0;
    }
  }

  .animate-confetti {
    animation: confetti 3s linear forwards;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }

  @keyframes typing {
    0%, 20% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    space-x: 4px;
  }

  .typing-indicator::before {
    content: "";
    margin-right: 8px;
    color: #6b7280;
    font-size: 14px;
  }

  .typing-indicator::after {
    content: "...";
    animation: typing 1.5s infinite;
    color: #6b7280;
    font-weight: bold;
  }
`;

// Add the styles to the document
const styleSheet = document.createElement("style");
styleSheet.innerText = animationStyles;
document.head.appendChild(styleSheet);

const MemeLoadingAnimation = () => {
  const loadingMessages = [
    "🚀 Loading some moon math...",
    "💎 Diamond hands in progress...",
    "🦍 Ape brain activation...",
    "🌙 Calculating moon potential...",
    "🔥 FOMO levels rising...",
    "💫 Stonks only go up...",
    "🎮 Leveling up your crypto game...",
    "🎯 Finding the next 100x...",
    "🧠 Smooth brain to wrinkle brain conversion...",
    "🌊 Riding the wave...",
  ];

  const [currentMessage, setCurrentMessage] = useState(loadingMessages[0]);
  const [currentEmoji, setCurrentEmoji] = useState("🚀");
  const [score, setScore] = useState(0);
  const [isSpinning, setIsSpinning] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * loadingMessages.length);
      const message = loadingMessages[randomIndex];
      setCurrentMessage(message);
      setCurrentEmoji(message.split(" ")[0]);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const handleEmojiClick = () => {
    setIsSpinning(true);
    setScore(prev => prev + 1);
    setShowConfetti(true);
    setTimeout(() => {
      setIsSpinning(false);
      setShowConfetti(false);
    }, 1000);
  };

  const handleDotClick = (index) => {
    const dots = document.querySelectorAll('.loading-dot');
    dots[index].classList.add('scale-150');
    setTimeout(() => {
      dots[index].classList.remove('scale-150');
    }, 200);
  };

  return (
    <div className="flex flex-col items-center space-y-4 animate-pulse">
      <div 
        className={`text-4xl cursor-pointer transition-transform duration-300 ${isSpinning ? 'animate-spin' : 'animate-bounce'}`}
        onClick={handleEmojiClick}
        title="Click for points!"
      >
        {currentEmoji}
      </div>
      {showConfetti && (
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-confetti"
              style={{
                left: `${Math.random() * 100}%`,
                top: '-10px',
                backgroundColor: ['#FF69B4', '#9370DB', '#FFD700', '#00CED1'][Math.floor(Math.random() * 4)],
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${1 + Math.random() * 2}s`,
              }}
            />
          ))}
        </div>
      )}
      <div className="text-center">
        <p className="text-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-transparent bg-clip-text">
          {currentMessage}
        </p>
        <p className="text-sm text-gray-500 mt-1">Score: {score} 🎯</p>
        <div className="mt-2 flex justify-center space-x-2">
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className="loading-dot w-2 h-2 bg-purple-500 rounded-full animate-bounce cursor-pointer transition-transform"
              style={{ animationDelay: `${index * 150}ms` }}
              onClick={() => handleDotClick(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const Chat = () => {
  const textareaRef = useRef(null);
  const [maxRows, setMaxRows] = useState(3);
  const [showModal, setShowModal] = useState(false);
  const [showInvestmentModal, setShowInvestmentModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [selectedTag, setSelectedTag] = useState("Default");
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const userData = secureLocalStorage.getItem("userData");
  const userInfo = secureLocalStorage.getItem("userInvestment");
  const {
    chatHistory,
    setChatHistory,
    socketRef,
    setIsResponseStreaming,
    isResponseStreaming,
    stopAIResponse,
    resetInactivityTimer,
    isMemeMode,
    sendMemeMessage,
    memeTheme,
    memeThemes,
    // Conversation context
    conversationContext,
    updateConversationContext,
    syncConversationContext,
    resetConversationContext,
    isFollowUpQuestion,
  } = useContext(AppContext);
  const chatContainerRef = useRef(null);
  const [sessionId, setSessionId] = useState(
    secureLocalStorage.getItem("session_id")
  );

  const updateMaxRows = () => {
    if (window.innerWidth >= 1024) {
      setMaxRows(6); // lg and above
    } else if (window.innerWidth >= 768) {
      setMaxRows(5); // md
    } else if (window.innerWidth >= 640) {
      setMaxRows(4); // sm
    } else {
      setMaxRows(3); // below sm
    }
  };
  useEffect(() => {
    updateMaxRows(); // Set initial max rows
    window.addEventListener("resize", updateMaxRows);

    return () => {
      window.removeEventListener("resize", updateMaxRows);
    };
  }, []);

  // Handle responsive mobile detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    secureLocalStorage.setItem("chatHistory", JSON.stringify(chatHistory));
    
    // Only auto-scroll if shouldAutoScroll is true
    if (shouldAutoScroll && chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }

    // Show feedback modal after 1-2 chat interactions
    const hasShownFeedback = secureLocalStorage.getItem("hasShownFeedback");
    if (!hasShownFeedback && chatHistory.length >= 2) {
      setShowFeedbackModal(true);
      secureLocalStorage.setItem("hasShownFeedback", "true");
    }
  }, [chatHistory, shouldAutoScroll]);

  useEffect(() => {
    if (!sessionId) {
      // If no session_id, create a new one
      const startNewChat = async () => {
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/chatbot/create-session`
        );
        const newSessionId = response.data.session_id;
        setSessionId(newSessionId);
        secureLocalStorage.setItem("session_id", newSessionId);
        
        // Reset conversation context for new session
        resetConversationContext();
      };
      startNewChat();
    } else {
      if (socketRef.current) {
        socketRef.current.close();
      }
      socketRef.current = new WebSocket(
        `wss://cryptopmentor-backend.onrender.com/chatbot/ai-response/${sessionId}`
      );
      socketRef.current.onopen = () => {
        console.log("WebSocket connection established");
        // Sync conversation context when WebSocket connects
        if (isMemeMode) {
          syncConversationContext(sessionId);
        }
      };
      socketRef.current.onmessage = (event) => {
        const messageData = JSON.parse(event.data);

        const botResponse = messageData.data.v;
        if (typeof botResponse === "string" && botResponse.trim() !== "") {
          const cleanResponse = botResponse;

          if (cleanResponse === "__END_OF_RESPONSE__") {
            setIsResponseStreaming(false);
          } else {
            setIsResponseStreaming(true);
            setChatHistory((prev) => {
              const updatedChatHistory = [...prev];
              const lastMessage =
                updatedChatHistory[updatedChatHistory.length - 1];

              updatedChatHistory[updatedChatHistory.length - 1] = {
                ...lastMessage,
                answer: lastMessage.answer + " " + cleanResponse,
              };

              return updatedChatHistory;
            });
          }
        }
      };

      socketRef.current.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    }

    const storedChatHistory = secureLocalStorage.getItem("chatHistory");
    if (storedChatHistory) {
      setChatHistory(JSON.parse(storedChatHistory));
    }
  }, [sessionId]);

  useEffect(() => {
    // Check if the flag to show the investment modal is set
    const showModalFlag = secureLocalStorage.getItem("showInvestmentModal");
    if (showModalFlag === "true") {
      setShowInvestmentModal(true);
      // Clear the flag so the modal doesn't show again on subsequent loads
      secureLocalStorage.removeItem("showInvestmentModal");
    }
  }, []); // Empty dependency array ensures this runs only once on mount

  // Handle scroll behavior during response streaming
  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = chatContainer;
      const isAtBottom = scrollHeight - scrollTop <= clientHeight + 10; // 10px tolerance
      
      // Enable auto-scroll when user is at the bottom or when not streaming
      if (isAtBottom || !isResponseStreaming) {
        setShouldAutoScroll(true);
      } else {
        // Disable auto-scroll if user scrolls up during streaming
        setShouldAutoScroll(false);
      }
    };

    chatContainer.addEventListener('scroll', handleScroll);
    
    return () => {
      chatContainer.removeEventListener('scroll', handleScroll);
    };
  }, [isResponseStreaming]);

  // Reset auto-scroll when starting a new message
  useEffect(() => {
    if (isResponseStreaming) {
      setShouldAutoScroll(true);
    }
  }, [isResponseStreaming]);

  const handleSendMessage = async () => {
    // Reset inactivity timer when user sends a message
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }

    if (!userData) {
      setShowModal(true);
      return;
    }
    if (
      !inputValue.trim() ||
      isResponseStreaming ||
      (chatHistory.length > 0 &&
        chatHistory[chatHistory.length - 1].answer === "")
    )
      return;

    const newMessage = {
      question: inputValue,
      answer: "",
      userAvatar: "/assets/User Avatar.svg",
      aiAvatar: "/assets/AI Avatar.svg",
      isFollowUp: isMemeMode ? isFollowUpQuestion(inputValue) : false,
      contextualReferences: isMemeMode ? extractContextualReferences(inputValue) : [],
      selectedTag,
    };

    setChatHistory((prev) => [...prev, newMessage]);
    const currentInput = inputValue;
    setInputValue("");
    setIsResponseStreaming(true);

    // Use meme mode API if meme mode is enabled
    if (isMemeMode) {
      try {
        const memeResponse = await sendMemeMessage(
          currentInput,
          userInfo.data,
          selectedTag
        );

        // Update the chat history with the meme response and context metadata
        setChatHistory((prev) => {
          const updatedChatHistory = [...prev];
          const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];

          updatedChatHistory[updatedChatHistory.length - 1] = {
            ...lastMessage,
            answer: memeResponse.analysis || "🎉 Meme mode response received! 🚀",
            contextMetadata: memeResponse.contextMetadata || {},
            enhancedRecommendations: memeResponse.enhancedRecommendations || {},
            searchEnabled: memeResponse.searchEnabled || false,
            timestamp: memeResponse.timestamp || new Date().toISOString()
          };

          return updatedChatHistory;
        });

        setIsResponseStreaming(false);
      } catch (error) {
        console.error('Meme mode API error:', error);
        
        // Enhanced error handling with context awareness
        const errorMessage = handleMemeApiError(error, currentInput);
        
        // Update chat with error message
        setChatHistory((prev) => {
          const updatedChatHistory = [...prev];
          const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];

          updatedChatHistory[updatedChatHistory.length - 1] = {
            ...lastMessage,
            answer: errorMessage,
            isError: true,
            errorType: error.response?.status || 'unknown'
          };

          return updatedChatHistory;
        });

        setIsResponseStreaming(false);
        
        // Don't fallback to WebSocket for meme mode - show error instead
        return;
      }
    } else {
      // Use regular WebSocket for normal mode
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.send(
          JSON.stringify({
            query: currentInput,
            user_info: userInfo.data,
            selected_tag: selectedTag,
          })
        );
      }
    }
  };

  const handleInput = (e) => {
    // Reset inactivity timer when user types
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }

    setInputValue(e.target.value);

    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto"; // Reset height
      const maxHeight = maxRows * 24; // Calculate max height based on rows
      const newHeight = Math.min(textarea.scrollHeight, maxHeight);
      textarea.style.height = `${newHeight}px`;
      
      // Add scroll if content exceeds max height
      if (textarea.scrollHeight > maxHeight) {
        textarea.style.overflowY = "auto";
      } else {
        textarea.style.overflowY = "hidden";
      }
    }
  };
  const handleKeyDown = (e) => {
    // Reset inactivity timer when user presses a key
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }

    if (e.key === "Enter") {
      e.preventDefault();
      handleSendMessage();
      setInputValue("");
      
      // Reset textarea height after sending message
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = "auto";
        textarea.style.overflowY = "hidden";
      }
    }
  };

  const getTextClass = (text) => {
    const containsLongWord = text.split(" ").some((word) => word.length > 50);
    return containsLongWord ? "break-all" : "break-words";
  };

  // Extract contextual references from user message
  const extractContextualReferences = (message) => {
    const references = [];
    const lowerMessage = message.toLowerCase();

    // Coin reference patterns
    const coinPatterns = [
      { pattern: /\b(that coin|the coin|this coin)\b/gi, type: 'coin_reference' },
      { pattern: /\b(that token|the token|this token)\b/gi, type: 'token_reference' },
      { pattern: /\b(first|second|third|last|previous|earlier)\s+(coin|token|recommendation)\b/gi, type: 'positional_reference' },
      { pattern: /\b(it|that|this)\b/gi, type: 'pronoun_reference' }
    ];

    // Topic reference patterns
    const topicPatterns = [
      { pattern: /\b(tell me more|more info|details|explain|clarify|elaborate)\b/gi, type: 'clarification_request' },
      { pattern: /\b(what about|how about|why|when|where)\b/gi, type: 'follow_up_question' },
      { pattern: /\b(is it safe|risky|good|worth it|profitable)\b/gi, type: 'risk_assessment_request' },
      { pattern: /\b(should I|can I|would you|do you think)\b/gi, type: 'advice_request' }
    ];

    [...coinPatterns, ...topicPatterns].forEach(({ pattern, type }) => {
      const matches = message.match(pattern);
      if (matches) {
        matches.forEach(match => {
          references.push({
            type,
            text: match,
            position: message.indexOf(match)
          });
        });
      }
    });

    return references;
  };

  // Enhanced error handling for meme API
  const handleMemeApiError = (error, userMessage) => {
    const isFollowUp = isFollowUpQuestion(userMessage);
    
    if (error.response?.status === 503) {
      return `🚨 **AI Service Temporarily Down** 🚨

The meme coin analysis service is currently unavailable. This might be due to high demand or maintenance.

**What you can do:**
- Try again in a few minutes
- ${isFollowUp ? 'Your conversation context is preserved, so you can continue where you left off' : 'Start with a general meme coin question when service returns'}
- Check our status page for updates

**Your question:** "${userMessage}"
*We'll remember this when the service is back online!* 💎`;
    }

    if (error.response?.status === 429) {
      return `⏰ **Rate Limit Reached** ⏰

You're asking questions faster than our AI can handle! This is actually a good sign - you're really into crypto! 🚀

**What's happening:**
- Too many requests in a short time
- ${conversationContext.history.length > 0 ? `You've had ${conversationContext.history.length} exchanges in this conversation` : 'Multiple rapid requests detected'}

**What to do:**
- Wait about 30 seconds before trying again
- ${isFollowUp ? 'Your conversation context is saved' : 'Your question will be processed when you retry'}

*Patience pays off in crypto! 💎🙌*`;
    }

    if (error.response?.status === 400) {
      return `❌ **Message Processing Error** ❌

There was an issue processing your message. This could be due to:

**Possible causes:**
- Message format issue
- ${isFollowUp ? 'Context reference couldn\'t be resolved' : 'Missing required information'}
- Session synchronization problem

**Try this:**
- Rephrase your question more clearly
- ${isFollowUp ? 'Reference specific coins by name instead of "that coin"' : 'Include your investment profile in the message'}
- Start a new conversation if the issue persists

**Your message:** "${userMessage}"`;
    }

    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return `🌐 **Connection Issue** 🌐

Can't reach the meme coin analysis service right now.

**What's happening:**
- Network connectivity issue
- Service might be temporarily offline
- ${isFollowUp ? 'Context preserved locally' : 'Your message is saved'}

**Quick fixes:**
- Check your internet connection
- Try refreshing the page
- ${conversationContext.history.length > 0 ? 'Your conversation history is safe' : 'You can retry the same question'}

*Diamond hands even during technical difficulties! 💎*`;
    }

    // Generic error with context awareness
    return `🤖 **Unexpected Error** 🤖

Something went wrong while processing your meme coin request.

**Context Status:**
- ${conversationContext.history.length > 0 ? `Conversation history: ${conversationContext.history.length} exchanges` : 'New conversation'}
- ${isFollowUp ? 'Follow-up question detected' : 'Initial question'}
- Session: ${sessionId ? 'Active' : 'Needs refresh'}

**What to try:**
- Refresh the page and try again
- ${isFollowUp ? 'Ask your question with more specific details' : 'Start with a complete investment profile'}
- Clear conversation history if issues persist

**Error details:** ${error.message || 'Unknown error'}

*We're working to fix this! Your crypto journey continues! 🚀*`;
  };

  const TagSelector = () => {
    const tagData = [
      {
        full: "Explain crypto in simple terms",
        mobile: "Simple terms",
        desktop: "Explain crypto in simple terms"
      },
      {
        full: "Explain crypto using analogies",
        mobile: "Analogies",
        desktop: "Explain crypto using analogies"
      },
      {
        full: "Explain crypto using real-world examples",
        mobile: "Real examples",
        desktop: "Explain crypto using real-world examples"
      }
    ];

    return (
      <div className="flex flex-wrap items-center gap-1.5 sm:gap-2">
        {tagData.map((tag) => {
          const label = isMobile ? tag.mobile : tag.desktop;
          const emoji = isMemeMode ? (tag.full === "Explain crypto in simple terms" ? "🧩" : tag.full === "Explain crypto using analogies" ? "🔗" : "🌍") : "";
          const selected = selectedTag === tag.full;

          const baseClasses =
            "px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-xs font-medium transition-all border focus:outline-none focus:ring-2 focus:ring-offset-1 whitespace-nowrap";
          const selectedClasses = isMemeMode
            ? `bg-gradient-to-r ${memeThemes[memeTheme]?.primary || "from-purple-500 to-pink-500"} text-white shadow-sm border-transparent focus:ring-purple-200`
            : "bg-gray-900 text-white shadow-sm border-gray-900 focus:ring-gray-300";
          const unselectedClasses = "bg-white text-gray-700 border-gray-200 hover:bg-gray-100";

          return (
            <button
              key={tag.full}
              type="button"
              aria-pressed={selected}
              className={`${baseClasses} ${selected ? selectedClasses : unselectedClasses} ${
                isMobile ? 'min-h-[32px] text-xs' : 'min-h-[36px]'
              }`}
              onClick={() => {
                if (selectedTag === tag.full) {
                  setSelectedTag("Default"); // Deselect if the same tag is clicked again
                } else {
                  setSelectedTag(tag.full); // Select the new tag
                }
              }}
            >
              {isMemeMode && <span className="mr-0.5 sm:mr-1">{emoji}</span>}
              {label}
            </button>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <div className="mb-12 sm:mb-0">
        <Header />
      </div>
      {chatHistory.length > 0 ? (
        <div className=" h-[calc(100vh-200px)]">
          <div
            ref={chatContainerRef}
            className="p-6 flex space-y-6 flex-col items-center max-h-[63vh] min-h-[63vh] sm:min-h-[75vh] sm:max-h-[75vh] overflow-y-auto overflow-x-hidden"
          >
            {/* Meme Mode Indicator for Chat Area */}
            {isMemeMode && (
              <div className="w-full flex justify-center items-center space-x-3 mb-4">
                <div className={`bg-gradient-to-r ${memeThemes[memeTheme]?.primary || 'from-purple-500 to-pink-500'} text-white px-4 py-2 rounded-full shadow-lg animate-pulse`}>
                  <span className="text-xs font-medium">
                    {memeThemes[memeTheme]?.emoji} MEME MODE ON 🚀
                    {!conversationContext.isEmpty && (
                      <span className="ml-2 text-xs opacity-75">
                        💬 Context Active ({conversationContext.history.length})
                      </span>
                    )}
                  </span>
                </div>
                {!conversationContext.isEmpty && (
                  <button
                    onClick={resetConversationContext}
                    className="bg-white text-gray-700 hover:text-gray-900 px-3 py-1 rounded-full text-xs border border-gray-200 hover:border-gray-300 transition-colors shadow-sm"
                    title="Reset conversation context"
                  >
                    🔄 New Chat
                  </button>
                )}
              </div>
            )}

            {/* Conversation Context Indicator */}
            {isMemeMode && !conversationContext.isEmpty && conversationContext.contextSummary && (
              <div className="w-full max-w-2xl mx-auto mb-4">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-sm">🧠</span>
                    <span className="text-xs font-medium text-gray-700">Conversation Context</span>
                  </div>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    {conversationContext.contextSummary}
                  </p>
                  {conversationContext.lastRecommendations.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {conversationContext.lastRecommendations.slice(0, 3).map((rec, index) => (
                        <span key={index} className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                          {rec.name || rec.symbol}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 gap-6 place-items-center w-full">
              {chatHistory.map((item, index) => (
                <div key={index} className="w-full max-w-2xl space-y-4 mx-auto">
                  {/* User Message with Context Indicators */}
                  <div className="grid grid-cols-[1fr_auto] gap-4 items-center justify-items-end">
                    <div className={`p-2 border rounded-3xl max-w-full ${
                      item.isFollowUp ? 'border-purple-300 bg-purple-50' : ''
                    }`}>
                      <div className="flex items-center justify-between space-x-4">
                        <div className="flex-1">
                          {/* Context indicators for follow-up questions */}
                          {isMemeMode && item.isFollowUp && (
                            <div className="flex items-center space-x-2 mb-2 px-4">
                              <span className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
                                💬 Follow-up
                              </span>
                              {item.contextualReferences && item.contextualReferences.length > 0 && (
                                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                                  🔗 References context
                                </span>
                              )}
                            </div>
                          )}
                          <p
                            className={`text-sm text-end px-4 ${getTextClass(
                              item.question
                            )}`}
                          >
                            {item.question}
                          </p>
                          {/* Show contextual references if available */}
                          {isMemeMode && item.contextualReferences && item.contextualReferences.length > 0 && (
                            <div className="mt-2 px-4">
                              <div className="text-xs text-gray-500 space-y-1">
                                {item.contextualReferences.slice(0, 3).map((ref, refIndex) => (
                                  <div key={refIndex} className="flex items-center space-x-1">
                                    <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                                    <span>References: "{ref.text}" ({ref.type.replace('_', ' ')})</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <button className="p-2 bg-pink-100 rounded-full">
                            {/* <img src={item.editIcon} alt="Edit" /> */}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="border rounded-full">
                      <img
                        src={item.userAvatar}
                        alt="User Avatar"
                        className="w-10"
                      />
                    </div>
                  </div>

                  {/* AI Response with Enhanced Metadata */}
                  <div className="grid grid-cols-[auto_1fr] gap-4">
                    <img src={item.aiAvatar} alt="AI Avatar" className="w-10" />
                    <div className={`p-2 bg-white rounded-2xl shadow-sm max-w-2xl ${
                      item.isError ? 'border-l-4 border-red-400 bg-red-50' : ''
                    }`}>
                      {/* Context metadata display for meme mode */}
                      {isMemeMode && item.contextMetadata && !item.isError && (
                        <div className="mb-3 p-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center space-x-3">
                              {item.contextMetadata.isFollowUp && (
                                <span className="text-purple-600 font-medium">💬 Contextual Response</span>
                              )}
                              {item.searchEnabled && (
                                <span className="text-green-600 font-medium">🔍 Live Data</span>
                              )}
                              {item.contextMetadata.conversationLength > 0 && (
                                <span className="text-blue-600">
                                  📊 Exchange #{item.contextMetadata.conversationLength}
                                </span>
                              )}
                            </div>
                            {item.timestamp && (
                              <span className="text-gray-500">
                                {new Date(item.timestamp).toLocaleTimeString()}
                              </span>
                            )}
                          </div>
                          
                          {/* Show recent recommendations context */}
                          {item.contextMetadata.lastRecommendations && 
                           item.contextMetadata.lastRecommendations.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-1">
                              <span className="text-xs text-gray-600">Recent coins:</span>
                              {item.contextMetadata.lastRecommendations.map((rec, recIndex) => (
                                <span key={recIndex} className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                                  {rec.name || rec.symbol || rec}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      )}

                      <div className="md:min-w-[596px] mt-2 px-2 text-sm text-gray-700 leading-loose">
                        {item.answer === "" ? (
                          isMemeMode ? (
                            <MemeLoadingAnimation />
                          ) : (
                            <div className="typing-indicator"></div>
                          )
                        ) : (
                          <ReactMarkDown 
                            className={`animate-fadeIn ${item.isError ? 'text-red-700' : ''}`}
                            components={{
                              a: ({ node, ...props }) => {
                                const isReferralLink = props.href?.includes('coinbase.com/join');
                                return (
                                  <a
                                    {...props}
                                    target={isReferralLink ? "_blank" : undefined}
                                    rel={isReferralLink ? "noopener noreferrer" : undefined}
                                    className={isReferralLink ? "text-blue-600 hover:text-blue-800 underline font-medium" : ""}
                                  />
                                );
                              }
                            }}
                          >
                            {item.answer}
                          </ReactMarkDown>
                        )}
                      </div>

                      {/* Enhanced recommendations display */}
                      {isMemeMode && item.enhancedRecommendations && 
                       item.enhancedRecommendations.newRecommendations && 
                       item.enhancedRecommendations.newRecommendations.length > 0 && (
                        <div className="mt-3 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm font-medium text-green-700">🎯 New Recommendations</span>
                            <span className="text-xs text-gray-500">
                              ({item.enhancedRecommendations.newRecommendations.length} coins)
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {item.enhancedRecommendations.newRecommendations.slice(0, 5).map((rec, recIndex) => (
                              <div key={recIndex} className="bg-white border border-green-300 rounded-lg px-3 py-2">
                                <div className="text-sm font-medium text-green-800">
                                  {rec.name || rec.symbol}
                                </div>
                                {rec.symbol && rec.name && (
                                  <div className="text-xs text-gray-600">${rec.symbol}</div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Error recovery options */}
                      {item.isError && (
                        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-yellow-700">
                              💡 Try rephrasing your question or refresh the page
                            </span>
                            <button
                              onClick={() => {
                                // Retry the same question
                                setInputValue(item.question);
                                if (textareaRef.current) {
                                  textareaRef.current.focus();
                                }
                              }}
                              className="text-xs bg-yellow-200 hover:bg-yellow-300 text-yellow-800 px-2 py-1 rounded transition-colors"
                            >
                              🔄 Retry
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="w-full max-w-xl md:max-w-3xl mx-auto">
            <div className="relative flex items-center w-full mb-10 md:mb-20 bg-[#fff] border rounded-2xl group focus-within:border-2 focus-within:border-pink-200">
              <div className="w-[95%] px-6  pt-4 md:pt-0 ">
                <TagSelector />
                <textarea
                  ref={textareaRef}
                  value={inputValue}
                  onKeyDown={handleKeyDown}
                  onChange={handleInput}
                  rows={1}
                  className={`w-full min-h-[56px] overflow-y-hidden bg-transparent mt-2 rounded-full md:py-4 pl-4 pr-12 resize-none outline-none break-words focus:ring-0 ${
                    isResponseStreaming ? "text-gray-400" : ""
                  }`}
                  placeholder={
                    isResponseStreaming
                      ? "AI is responding... Click stop button to cancel"
                      : "Ask CryptoMentor..."
                  }
                  wrap="soft"
                  readOnly={isResponseStreaming}
                  autoFocus
                  style={{ height: "auto" }}
                />
              </div>
              {isResponseStreaming ? (
                <button
                  onClick={stopAIResponse}
                  className="absolute right-3 p-2 rounded-full bg-black hover:bg-black cursor-pointer transition-colors"
                  title="Stop generating"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              ) : (
                <button
                  onClick={handleSendMessage}
                  className="absolute right-3 p-2 rounded-full bg-black hover:bg-gray-800 cursor-pointer transition-colors"
                >
                  <img
                    src="/assets/send.svg"
                    alt="Send"
                    className="w-4 h-4 text-white"
                  />
                </button>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col justify-center items-center h-auto sm:h-[calc(100vh-100px)] px-4">
          {/* Meme Mode Indicator */}
          {isMemeMode && (
            <div className="mb-6">
              <div className={`bg-gradient-to-r ${memeThemes[memeTheme]?.primary || 'from-purple-500 to-pink-500'} text-white px-6 py-3 rounded-full shadow-lg animate-pulse`}>
                <span className="text-sm font-medium">
                  {memeThemes[memeTheme]?.emoji} MEME MODE ACTIVATED 🚀 - Ready to moon with epic crypto advice!
                </span>
              </div>
            </div>
          )}

          <h1 className={`text-2xl sm:text-3xl md:text-5xl font-semibold mb-6 text-center transition-all duration-300 ${
            isMemeMode
              ? `text-transparent bg-clip-text bg-gradient-to-r ${memeThemes[memeTheme]?.secondary || 'from-purple-400 to-pink-400'} animate-pulse`
              : ''
          }`}>
            WELCOME TO CRYPTOMENTOR{isMemeMode ? ` ${memeThemes[memeTheme]?.emoji}` : ''}
          </h1>
          <p className={`text-center text-sm md:text-base mb-6 transition-colors duration-300 ${
            isMemeMode ? 'text-white font-medium' : 'text-[#808080]'
          }`}>
            {isMemeMode
              ? "🚀 Crypto Investing Made Epic - Let's get this bread! 💎🙌"
              : "Crypto Investing Made Simple"
            }
          </p>
          <div className="w-full max-w-xl md:max-w-3xl mx-auto">
            <div className="relative flex items-center w-full mb-10 md:mb-20 bg-[#fff] border rounded-2xl group focus-within:border-2 focus-within:border-pink-200">
              <div className="w-[95%] px-6  pt-4 md:pt-0 ">
                <TagSelector />
                <textarea
                  ref={textareaRef}
                  value={inputValue}
                  onKeyDown={handleKeyDown}
                  onChange={handleInput}
                  rows={1}
                  className={`w-full min-h-[56px] overflow-y-hidden bg-transparent mt-2 rounded-full md:py-4 pl-4 pr-12 resize-none outline-none break-words focus:ring-0 ${
                    isResponseStreaming ? "text-gray-400" : ""
                  }`}
                  placeholder={
                    isResponseStreaming
                      ? "AI is responding... Click stop button to cancel"
                      : "Ask CryptoMentor..."
                  }
                  wrap="soft"
                  readOnly={isResponseStreaming}
                  autoFocus
                  style={{ height: "auto" }}
                />
              </div>
              {isResponseStreaming ? (
                <button
                  onClick={stopAIResponse}
                  className="absolute right-3 p-2 rounded-full bg-black hover:bg-black cursor-pointer transition-colors"
                  title="Stop generating"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              ) : (
                <button
                  onClick={handleSendMessage}
                  className="absolute right-3 p-2 rounded-full bg-black hover:bg-gray-800 cursor-pointer transition-colors"
                >
                  <img
                    src="/assets/send.svg"
                    alt="Send"
                    className="w-4 h-4 text-white"
                  />
                </button>
              )}
            </div>
          </div>

          {/* Removed the standalone stop button */}

          <div className="flex flex-wrap md:flex-nowrap justify-center items-center max-w-4xl gap-8 md:space-x-16">
            <div className="w-40 md:w-48 flex flex-col justify-center items-center">
              <img className="w-5 mb-2" src="/assets/stars.svg" alt="Stars" />
              <p className="font-medium text-center">
                Clear and actionable insights
              </p>
              <p className="text-center text-sm text-[#808080]">
                Get simplified crypto investment advice and recommendations,
                free from jargon.
              </p>
            </div>
            <div className="w-40 md:w-48 flex flex-col justify-center items-center">
              <img
                className="w-5 mb-2"
                src="/assets/my_location.svg"
                alt="Location"
              />
              <p className="font-medium text-center">
                Tailored crypto strategies
              </p>
              <p className="text-center text-sm text-[#808080]">
                Receive personalized portfolio suggestions and market insights,
                crafted for your unique goals.
              </p>
            </div>
            <div className="w-40 md:w-48 flex flex-col justify-center items-center">
              <img className="w-5 mb-2" src="/assets/vector.svg" alt="Vector" />
              <p className="font-medium text-center">
                Smarter, faster decisions
              </p>
              <p className="text-center text-sm text-[#808080]">
                Save time with data-driven crypto advice powered by cutting-edge
                AI technology.
              </p>
            </div>
          </div>
        </div>
      )}
      <InvestmentModal
        showModal={showInvestmentModal}
        setShowModal={setShowInvestmentModal}
      />
      <Modal isOpen={showModal} onClose={() => setShowModal(false)} />
      <FeedbackModal 
        isOpen={showFeedbackModal} 
        onClose={() => setShowFeedbackModal(false)} 
      />
    </>
  );
};

export default Chat;
