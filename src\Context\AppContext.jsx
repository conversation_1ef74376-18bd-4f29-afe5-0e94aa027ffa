import axios from "axios";
import React, { createContext, useEffect, useRef, useState } from "react";
import secureLocalStorage from "react-secure-storage";
import { jwtDecode } from "jwt-decode";

export const AppContext = createContext();

export const AppProvider = ({ children }) => {
  const [profileData, setProfileData] = useState();
  const [chatHistory, setChatHistory] = useState([]);
  const [isResponseStreaming, setIsResponseStreaming] = useState(false);
  const [isMemeMode, setIsMemeMode] = useState(false);
  const [memeTheme, setMemeTheme] = useState('purple');
  
  // Conversation context state
  const [conversationContext, setConversationContext] = useState({
    history: [],
    userProfile: null,
    contextSummary: '',
    lastRecommendations: [],
    sessionId: null,
    isEmpty: true
  });

  const socketRef = useRef(null);
  const inactivityTimeoutRef = useRef(null);

  // Meme mode theme definitions
  const memeThemes = {
    purple: {
      name: '💜 Purple Dream',
      primary: 'from-purple-500 to-pink-500',
      secondary: 'from-purple-400 to-pink-400',
      background: 'from-purple-400 via-pink-500 to-red-500',
      accent: 'purple-500',
      emoji: '💜'
    },
    ocean: {
      name: '🌊 Ocean Vibes',
      primary: 'from-blue-500 to-cyan-500',
      secondary: 'from-blue-400 to-cyan-400',
      background: 'from-blue-400 via-cyan-500 to-teal-500',
      accent: 'blue-500',
      emoji: '🌊'
    },
    sunset: {
      name: '🌅 Sunset Glow',
      primary: 'from-orange-500 to-red-500',
      secondary: 'from-orange-400 to-red-400',
      background: 'from-orange-400 via-red-500 to-pink-500',
      accent: 'orange-500',
      emoji: '🌅'
    },
    galaxy: {
      name: '🌌 Galaxy Mode',
      primary: 'from-indigo-500 to-purple-500',
      secondary: 'from-indigo-400 to-purple-400',
      background: 'from-indigo-400 via-purple-500 to-pink-500',
      accent: 'indigo-500',
      emoji: '🌌'
    },
    neon: {
      name: '⚡ Neon Blast',
      primary: 'from-green-500 to-yellow-500',
      secondary: 'from-green-400 to-yellow-400',
      background: 'from-green-400 via-yellow-500 to-orange-500',
      accent: 'green-500',
      emoji: '⚡'
    },
    fire: {
      name: '🔥 Fire Storm',
      primary: 'from-red-500 to-yellow-500',
      secondary: 'from-red-400 to-yellow-400',
      background: 'from-red-400 via-orange-500 to-yellow-500',
      accent: 'red-500',
      emoji: '🔥'
    }
  };

  // Function to stop the AI response
  const stopAIResponse = () => {
    if (socketRef.current) {
      // Close the current WebSocket connection
      socketRef.current.close();

      // Set streaming to false to enable the input field
      setIsResponseStreaming(false);

      // Create a new WebSocket connection with the same session ID
      const sessionId = secureLocalStorage.getItem("session_id");
      if (sessionId) {
        socketRef.current = new WebSocket(
          `wss://cryptopmentor-backend.onrender.com/chatbot/ai-response/${sessionId}`
        );

        socketRef.current.onopen = () => {
          console.log("WebSocket connection re-established after stopping");
        };

        socketRef.current.onmessage = (event) => {
          const messageData = JSON.parse(event.data);
          const botResponse = messageData.data.v;

          if (typeof botResponse === "string" && botResponse.trim() !== "") {
            const cleanResponse = botResponse;

            if (cleanResponse === "__END_OF_RESPONSE__") {
              setIsResponseStreaming(false);
            } else {
              setIsResponseStreaming(true);
              setChatHistory((prev) => {
                const updatedChatHistory = [...prev];
                const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];

                updatedChatHistory[updatedChatHistory.length - 1] = {
                  ...lastMessage,
                  answer: lastMessage.answer + " " + cleanResponse,
                };

                return updatedChatHistory;
              });
            }
          }
        };

        socketRef.current.onerror = (error) => {
          console.error("WebSocket error:", error);
        };
      }
    }
  };

  const getUserData = async () => {
    const userData = secureLocalStorage.getItem("userData"); // Retrieve email from secure local storage
    if (!userData?.email) {
      console.error("No email found in secure local storage.");
      return;
    }
    try {
      const formData = new FormData();
      formData.append("email", userData.email);
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/user_profile/get_user_info`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const userInfo = response.data;
      setProfileData(userInfo);
      if (userInfo) {
        secureLocalStorage.setItem("userInvestment", userInfo);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  useEffect(() => {
    const authToken = secureLocalStorage.getItem("authToken");
    if (authToken) {
      getUserData();
    }

    // Load meme mode preference
    const savedMemeMode = secureLocalStorage.getItem("memeMode");
    if (savedMemeMode !== null) {
      setIsMemeMode(savedMemeMode);
    }

    // Load meme theme preference
    const savedMemeTheme = secureLocalStorage.getItem("memeTheme");
    if (savedMemeTheme) {
      setMemeTheme(savedMemeTheme);
    }

    // Load conversation context from local storage
    const savedContext = secureLocalStorage.getItem("conversationContext");
    if (savedContext) {
      setConversationContext(savedContext);
    }

    // Sync with backend if session exists
    const sessionId = secureLocalStorage.getItem("session_id");
    if (sessionId && authToken) {
      syncConversationContext(sessionId);
    }
  }, []);

  const logout = () => {
    setProfileData(null);
    setChatHistory([]);
    resetConversationContext();
    secureLocalStorage.clear();
    // localStorage.clear();
  };
  const checkTokenExpiration = () => {
    const token = secureLocalStorage.getItem("authToken");
    if (token) {
      try {
        const { exp } = jwtDecode(token);
        if (exp * 1000 < Date.now()) {
          logout();
        }
      } catch (error) {
        console.error("Invalid token:", error);
        logout();
      }
    }
  };
  useEffect(() => {
    checkTokenExpiration();
  }, []);

  // Function to toggle meme mode and reset context if switching modes
  const toggleMemeMode = () => {
    const newMemeMode = !isMemeMode;
    setIsMemeMode(newMemeMode);
    secureLocalStorage.setItem("memeMode", newMemeMode);

    // Reset conversation context when switching modes to avoid confusion
    resetConversationContext();

    // Apply meme mode to document root for CSS variables
    if (newMemeMode) {
      document.documentElement.classList.add('meme-mode');
      applyMemeTheme(memeTheme);
    } else {
      document.documentElement.classList.remove('meme-mode');
      // Remove theme classes
      Object.keys(memeThemes).forEach(theme => {
        document.documentElement.classList.remove(`meme-theme-${theme}`);
      });
    }
  };

  const changeMemeTheme = (newTheme) => {
    setMemeTheme(newTheme);
    secureLocalStorage.setItem("memeTheme", newTheme);
    if (isMemeMode) {
      applyMemeTheme(newTheme);
    }
  };

  const applyMemeTheme = (theme) => {
    // Remove all existing theme classes
    Object.keys(memeThemes).forEach(t => {
      document.documentElement.classList.remove(`meme-theme-${t}`);
    });
    // Add the new theme class
    document.documentElement.classList.add(`meme-theme-${theme}`);
  };

  // Function to send message to meme mode API with conversation context
  const sendMemeMessage = async (message, userInfo, selectedTag) => {
    try {
      const sessionId = secureLocalStorage.getItem("session_id");
      const response = await axios.post('https://memecoin-backend-ym1s.onrender.com/api/financial-advisor/analyze', {
        message: message,
        sessionId: sessionId,
        context: conversationContext,
        selected_tag: selectedTag,
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Update conversation context with the new exchange
      if (response.data && sessionId) {
        updateConversationContext(message, response.data, sessionId);
      }

      return response.data;
    } catch (error) {
      console.error('Error sending meme message:', error);
      throw error;
    }
  };

  // Update conversation context with new message exchange
  const updateConversationContext = (userMessage, aiResponse, sessionId) => {
    const newEntry = {
      id: Date.now().toString(),
      userMessage: userMessage,
      aiResponse: aiResponse.analysis || aiResponse.response || '',
      timestamp: new Date(),
      recommendationsGiven: aiResponse.recommendations || [],
      metadata: aiResponse.metadata || {}
    };

    setConversationContext(prevContext => {
      const updatedHistory = [...prevContext.history, newEntry];
      
      // Maintain maximum of 10 exchanges
      const recentHistory = updatedHistory.slice(-10);
      
      const updatedContext = {
        ...prevContext,
        history: recentHistory,
        sessionId: sessionId,
        isEmpty: false,
        lastRecommendations: aiResponse.recommendations || prevContext.lastRecommendations,
        contextSummary: generateContextSummary(recentHistory)
      };

      // Store in secure local storage for persistence
      secureLocalStorage.setItem("conversationContext", updatedContext);
      
      return updatedContext;
    });
  };

  // Generate context summary from conversation history
  const generateContextSummary = (history) => {
    if (history.length === 0) return '';
    
    const recentExchanges = history.slice(-3);
    const topics = [];
    const recommendations = [];

    recentExchanges.forEach(exchange => {
      // Extract crypto-related topics
      const cryptoTerms = [
        'memecoin', 'meme coin', 'token', 'crypto', 'investment', 'trading',
        'market cap', 'liquidity', 'rug pull', 'moon', 'degen', 'altcoin',
        'portfolio', 'risk', 'profit', 'loss', 'buy', 'sell', 'hold'
      ];

      cryptoTerms.forEach(term => {
        if (exchange.userMessage.toLowerCase().includes(term)) {
          topics.push(term);
        }
      });

      // Collect recommendations
      if (exchange.recommendationsGiven && exchange.recommendationsGiven.length > 0) {
        recommendations.push(...exchange.recommendationsGiven.map(rec => rec.name || rec.symbol));
      }
    });

    let summary = `Recent topics: ${[...new Set(topics)].join(', ')}`;
    
    if (recommendations.length > 0) {
      summary += `. Recent recommendations: ${[...new Set(recommendations)].join(', ')}`;
    }

    return summary;
  };

  // Sync conversation context with backend session
  const syncConversationContext = async (sessionId) => {
    try {
      const response = await axios.get(`https://memecoin-backend-ym1s.onrender.com/api/financial-advisor/context/${sessionId}`);
      
      if (response.data && response.data.context) {
        const backendContext = response.data.context;
        
        setConversationContext({
          history: backendContext.history || [],
          userProfile: backendContext.userProfile || profileData,
          contextSummary: backendContext.contextSummary || '',
          lastRecommendations: backendContext.lastRecommendations || [],
          sessionId: sessionId,
          isEmpty: !backendContext.history || backendContext.history.length === 0
        });

        // Store in secure local storage
        secureLocalStorage.setItem("conversationContext", {
          history: backendContext.history || [],
          userProfile: backendContext.userProfile || profileData,
          contextSummary: backendContext.contextSummary || '',
          lastRecommendations: backendContext.lastRecommendations || [],
          sessionId: sessionId,
          isEmpty: !backendContext.history || backendContext.history.length === 0
        });
      }
    } catch (error) {
      console.warn('Could not sync conversation context from backend:', error);
      // Load from local storage as fallback
      const localContext = secureLocalStorage.getItem("conversationContext");
      if (localContext) {
        setConversationContext(localContext);
      }
    }
  };

  // Reset conversation context for new session
  const resetConversationContext = () => {
    const newContext = {
      history: [],
      userProfile: profileData,
      contextSummary: '',
      lastRecommendations: [],
      sessionId: secureLocalStorage.getItem("session_id"),
      isEmpty: true
    };

    setConversationContext(newContext);
    secureLocalStorage.setItem("conversationContext", newContext);
  };

  // Check if current message is a follow-up question
  const isFollowUpQuestion = (message) => {
    const followUpPatterns = [
      /\b(tell me more|more info|details|explain|clarify|elaborate)\b/gi,
      /\b(what about|how about|why|when|where)\b/gi,
      /\b(is it safe|risky|good|worth it|profitable)\b/gi,
      /\b(should I|can I|would you|do you think)\b/gi,
      /\b(that coin|the coin|this coin|it|that token|the token|this token)\b/gi,
      /\b(first|second|third|last|previous|earlier)\s+(coin|token|recommendation)\b/gi
    ];

    return followUpPatterns.some(pattern => pattern.test(message)) || conversationContext.history.length > 0;
  };

  const resetInactivityTimer = () => {
    // Clear any existing timeout
    if (inactivityTimeoutRef.current) {
      clearTimeout(inactivityTimeoutRef.current);
    }

    // Set a new timeout - 20 minutes (1200000 ms)
    inactivityTimeoutRef.current = setTimeout(() => {
      const token = secureLocalStorage.getItem("authToken");
      if (token) {
        logout();
        // Redirect to login page will happen automatically due to GuestRoute
        window.location.href = "/signin";
      }
    }, 1200000); // 20 minutes
  };

  // Set up activity listeners
  useEffect(() => {
    // Only set up listeners if user is logged in
    const token = secureLocalStorage.getItem("authToken");
    if (!token) return;

    // Initialize the timer
    resetInactivityTimer();

    // Event listeners for user activity
    const activityEvents = [
      'mousedown', 'mousemove', 'keypress',
      'scroll', 'touchstart', 'click'
    ];

    // Function to handle user activity
    const handleUserActivity = () => {
      resetInactivityTimer();
    };

    // Add event listeners
    activityEvents.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });

    // Cleanup function
    return () => {
      // Remove event listeners
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });

      // Clear the timeout
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
      }
    };
  }, []);

  // Apply meme mode class on initial load
  useEffect(() => {
    if (isMemeMode) {
      document.documentElement.classList.add('meme-mode');
      applyMemeTheme(memeTheme);
    } else {
      document.documentElement.classList.remove('meme-mode');
      // Remove theme classes
      Object.keys(memeThemes).forEach(theme => {
        document.documentElement.classList.remove(`meme-theme-${theme}`);
      });
    }
  }, [isMemeMode, memeTheme]);

  return (
    <AppContext.Provider
      value={{
        profileData,
        setProfileData,
        logout,
        setChatHistory,
        chatHistory,
        getUserData,
        socketRef,
        setIsResponseStreaming,
        isResponseStreaming,
        stopAIResponse,
        resetInactivityTimer, // Expose the reset function
        isMemeMode,
        toggleMemeMode,
        sendMemeMessage,
        memeTheme,
        memeThemes,
        changeMemeTheme,
        // Conversation context management
        conversationContext,
        setConversationContext,
        updateConversationContext,
        syncConversationContext,
        resetConversationContext,
        isFollowUpQuestion,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};
