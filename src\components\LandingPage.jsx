import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react";

const LandingPage = () => {
  const navigate = useNavigate();
  const [currentFeature, setCurrentFeature] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const features = [
    {
      icon: "mdi:chart-line",
      title: "AI-Powered Analysis",
      description: "Get intelligent crypto insights powered by advanced AI algorithms that analyze market trends, sentiment, and technical indicators in real-time."
    },
    {
      icon: "mdi:shield-check",
      title: "Secure & Reliable",
      description: "Your data is protected with enterprise-grade security, encrypted storage, and secure API connections to ensure your privacy."
    },
    {
      icon: "mdi:lightning-bolt",
      title: "Real-Time Updates",
      description: "Stay ahead with instant market updates, price alerts, and breaking news notifications delivered directly to your dashboard."
    },
    {
      icon: "mdi:target",
      title: "Personalized Strategies",
      description: "Tailored investment strategies based on your goals, risk tolerance, and market preferences with continuous optimization."
    }
  ];

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Crypto Investor",
      content: "CryptoMentor's AI insights helped me increase my portfolio by 150% in just 6 months. The personalized strategies are game-changing!",
      avatar: "👩‍💼"
    },
    {
      name: "Michael Rodriguez",
      role: "Day Trader",
      content: "The real-time analysis and alerts have transformed my trading strategy. I've never felt more confident in my crypto decisions.",
      avatar: "👨‍💻"
    },
    {
      name: "Emma Thompson",
      role: "Investment Advisor",
      content: "I recommend CryptoMentor to all my clients. The AI-powered insights are incredibly accurate and easy to understand.",
      avatar: "👩‍🎓"
    }
  ];

  const cryptoIcons = ["₿", "Ξ", "◊", "Ł", "⟐", "◈"];

  useEffect(() => {
    setIsVisible(true);
    const featureInterval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);

    const testimonialInterval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);

    return () => {
      clearInterval(featureInterval);
      clearInterval(testimonialInterval);
    };
  }, []);

  return (
    <div className="min-h-screen bg-[#fff5f8] relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Crypto Symbols */}
        {cryptoIcons.map((symbol, index) => (
          <div
            key={index}
            className="absolute text-gray-200 text-6xl animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${index * 0.5}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          >
            {symbol}
          </div>
        ))}

        {/* Gradient Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-200/30 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-pink-200/30 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(0,0,0,0.1) 1px, transparent 0)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>

        {/* Shooting Stars */}
        <div className="absolute top-20 left-10 w-1 h-1 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-40 left-1/3 w-1 h-1 bg-pink-400 rounded-full animate-ping" style={{ animationDelay: '6s' }}></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Header */}
        <header className="flex justify-between items-center p-4 sm:p-6 lg:p-8">
          <div className="flex items-center space-x-3">
            <img
              src="/assets/logo.png"
              alt="CryptoMentor"
              className="h-8 sm:h-10 md:h-12 w-auto"
            />
          </div>
          <div className="flex items-center space-x-2 sm:space-x-4">
            <button
              onClick={() => navigate("/signin")}
              className="px-3 sm:px-6 py-2 text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors duration-300 font-medium"
            >
              Sign In
            </button>
            <button
              onClick={() => navigate("/signup")}
              className="px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Get Started
            </button>
          </div>
        </header>

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12 sm:py-20">
          <div className={`text-center max-w-6xl mx-auto transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            {/* Badge */}
            <div className="inline-flex items-center px-3 sm:px-4 py-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full text-gray-700 text-xs sm:text-sm mb-6 sm:mb-8 animate-fade-in-up shadow-sm">
              <Icon icon="mdi:sparkles" className="mr-1 sm:mr-2 text-purple-600 text-sm sm:text-base" />
              <span className="hidden sm:inline">Powered by Advanced AI Technology</span>
              <span className="sm:hidden">AI-Powered Technology</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight px-2">
              Your AI-Powered
              <span className="block bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent">
                Crypto Mentor
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4">
              Navigate the crypto market with confidence using advanced AI insights,
              personalized strategies, and real-time analysis that adapts to your investment goals.
            </p>

            {/* Stats */}
            {/* <div className="grid grid-cols-2 md:flex md:flex-wrap justify-center gap-4 sm:gap-6 md:gap-8 mb-8 sm:mb-12 text-gray-500 px-4">
              <div className="text-center group cursor-pointer">
                <div className="text-2xl sm:text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">10K+</div>
                <div className="text-xs sm:text-sm group-hover:text-gray-700 transition-colors duration-300">Active Users</div>
              </div>
              <div className="text-center group cursor-pointer">
                <div className="text-2xl sm:text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">99.9%</div>
                <div className="text-xs sm:text-sm group-hover:text-gray-700 transition-colors duration-300">Uptime</div>
              </div>
              <div className="text-center group cursor-pointer">
                <div className="text-2xl sm:text-3xl font-bold text-gray-900 group-hover:text-pink-600 transition-colors duration-300">24/7</div>
                <div className="text-xs sm:text-sm group-hover:text-gray-700 transition-colors duration-300">AI Support</div>
              </div>
              <div className="text-center group cursor-pointer">
                <div className="text-2xl sm:text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">$2M+</div>
                <div className="text-xs sm:text-sm group-hover:text-gray-700 transition-colors duration-300">Assets Analyzed</div>
              </div>
            </div> */}

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-20 px-4">
              <button
                onClick={() => navigate("/signup")}
                className="group w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-base sm:text-lg font-semibold rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-purple-500/25 sm:min-w-[200px] relative overflow-hidden"
              >
                <span className="relative z-10 flex items-center justify-center">
                  Start Your Journey
                  <Icon icon="mdi:arrow-right" className="inline ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-700 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>
              <button
                onClick={() => navigate("/about-us")}
                className="group w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 border-2 border-gray-300 text-gray-700 text-base sm:text-lg font-semibold rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 sm:min-w-[200px] bg-white/80 backdrop-blur-sm"
              >
                <span className="flex items-center justify-center">
                  Learn More
                  <Icon icon="mdi:information-outline" className="inline ml-2 group-hover:rotate-12 transition-transform duration-300" />
                </span>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-col sm:flex-row flex-wrap justify-center items-center gap-4 sm:gap-6 md:gap-8 opacity-70 px-4">
              <div className="flex items-center space-x-2">
                <Icon icon="mdi:shield-check" className="text-green-600 text-lg sm:text-xl" />
                <span className="text-gray-600 text-xs sm:text-sm">Bank-Level Security</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon icon="mdi:clock-fast" className="text-blue-600 text-lg sm:text-xl" />
                <span className="text-gray-600 text-xs sm:text-sm">Real-Time Data</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon icon="mdi:account-group" className="text-purple-600 text-lg sm:text-xl" />
                <span className="text-gray-600 text-xs sm:text-sm">Happy Users</span>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
                Why Choose <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">CryptoMentor?</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                Experience the future of crypto investing with our cutting-edge AI technology and comprehensive market analysis.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-12 sm:mb-20">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`group relative p-4 sm:p-6 rounded-2xl backdrop-blur-sm border transition-all duration-500 transform hover:scale-105 cursor-pointer ${
                    currentFeature === index
                      ? 'bg-white border-purple-400/50 shadow-2xl shadow-purple-500/20'
                      : 'bg-white/80 hover:bg-white border-gray-200 hover:border-purple-300 hover:shadow-lg'
                  }`}
                >
                  <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mb-3 sm:mb-4 mx-auto transition-all duration-300 group-hover:rotate-12 ${
                    currentFeature === index
                      ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-600 group-hover:bg-purple-100 group-hover:text-purple-600'
                  }`}>
                    <Icon icon={feature.icon} className="text-lg sm:text-2xl" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors duration-300 text-center">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-xs sm:text-sm leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-center">
                    {feature.description}
                  </p>

                  {/* Hover Effect Overlay */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-50 to-blue-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
                How It <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Works</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                Get started with CryptoMentor in just three simple steps and begin your journey to smarter crypto investing.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 sm:gap-12 mb-12 sm:mb-16">
              <div className="text-center group px-4">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <span className="text-xl sm:text-2xl font-bold text-white">1</span>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mb-3 sm:mb-4">Sign Up & Connect</h3>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                  Create your account and securely connect your preferred crypto exchanges or wallets. Our platform supports all major exchanges.
                </p>
              </div>

              <div className="text-center group px-4">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <span className="text-xl sm:text-2xl font-bold text-white">2</span>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mb-3 sm:mb-4">Set Your Goals</h3>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                  Define your investment objectives, risk tolerance, and preferences. Our AI will customize strategies specifically for you.
                </p>
              </div>

              <div className="text-center group px-4">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-green-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <span className="text-xl sm:text-2xl font-bold text-white">3</span>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mb-3 sm:mb-4">Start Investing</h3>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                  Receive personalized recommendations, market insights, and real-time alerts to make informed investment decisions.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
                What Our <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">Users Say</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                Join thousands of satisfied investors who have transformed their crypto journey with CryptoMentor.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
              {testimonials.map((testimonial, index) => (
                <div
                  key={index}
                  className={`p-4 sm:p-6 md:p-8 rounded-2xl backdrop-blur-sm border transition-all duration-500 ${
                    currentTestimonial === index
                      ? 'bg-white border-yellow-400/50 shadow-2xl shadow-yellow-500/20 scale-105'
                      : 'bg-white/80 border-gray-200 hover:bg-white hover:shadow-lg'
                  }`}
                >
                  <div className="flex items-center mb-4 sm:mb-6">
                    <div className="text-3xl sm:text-4xl mr-3 sm:mr-4">{testimonial.avatar}</div>
                    <div>
                      <h4 className="text-base sm:text-lg font-semibold text-gray-900">{testimonial.name}</h4>
                      <p className="text-gray-600 text-xs sm:text-sm">{testimonial.role}</p>
                    </div>
                  </div>
                  <p className="text-gray-700 text-sm sm:text-base leading-relaxed italic mb-4">
                    "{testimonial.content}"
                  </p>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Icon key={i} icon="mdi:star" className="text-yellow-500 text-base sm:text-lg" />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
                Choose Your <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Plan</span>
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                Start free and upgrade as you grow. All plans include our core AI features with varying levels of access.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
              {/* Free Plan */}
              <div className="p-4 sm:p-6 md:p-8 rounded-2xl backdrop-blur-sm border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-lg">
                <div className="text-center mb-6 sm:mb-8">
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Starter</h3>
                  <div className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">Free</div>
                  <p className="text-sm sm:text-base text-gray-600">Perfect for beginners</p>
                </div>
                <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Basic AI insights
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    5 portfolio analyses per month
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Email support
                  </li>
                </ul>
                <button
                  onClick={() => navigate("/signup")}
                  className="w-full py-3 text-sm sm:text-base border-2 border-gray-300 text-gray-700 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300"
                >
                  Get Started
                </button>
              </div>

              {/* Pro Plan */}
              <div className="p-4 sm:p-6 md:p-8 rounded-2xl backdrop-blur-sm border-2 border-purple-400/50 bg-white relative md:transform md:scale-105 shadow-xl">
                <div className="absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
                <div className="text-center mb-6 sm:mb-8 mt-4 sm:mt-0">
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Professional</h3>
                  <div className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">$29<span className="text-base sm:text-lg text-gray-600">/month</span></div>
                  <p className="text-sm sm:text-base text-gray-600">For serious investors</p>
                </div>
                <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Advanced AI analysis
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Unlimited portfolio tracking
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Real-time alerts
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Priority support
                  </li>
                </ul>
                <button
                  onClick={() => navigate("/signup")}
                  className="w-full py-3 text-sm sm:text-base bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
                >
                  Start Free Trial
                </button>
              </div>

              {/* Enterprise Plan */}
              <div className="p-4 sm:p-6 md:p-8 rounded-2xl backdrop-blur-sm border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-lg">
                <div className="text-center mb-6 sm:mb-8">
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                  <div className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">$99<span className="text-base sm:text-lg text-gray-600">/month</span></div>
                  <p className="text-sm sm:text-base text-gray-600">For institutions</p>
                </div>
                <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Custom AI models
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    API access
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    Dedicated support
                  </li>
                  <li className="flex items-center text-sm sm:text-base text-gray-700">
                    <Icon icon="mdi:check" className="text-green-600 mr-3 flex-shrink-0" />
                    White-label options
                  </li>
                </ul>
                <button
                  onClick={() => navigate("/signup")}
                  className="w-full py-3 text-sm sm:text-base border-2 border-gray-300 text-gray-700 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300"
                >
                  Contact Sales
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
              Ready to Transform Your <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Crypto Journey?</span>
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-8 sm:mb-12 max-w-2xl mx-auto px-4">
              Join thousands of investors who trust CryptoMentor to guide their crypto investment decisions with AI-powered intelligence.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
              <button
                onClick={() => navigate("/signup")}
                className="w-full sm:w-auto px-8 sm:px-10 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-lg sm:text-xl font-semibold rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-purple-500/25 sm:min-w-[250px]"
              >
                Start Free Today
                <Icon icon="mdi:rocket-launch" className="inline ml-2" />
              </button>
              <button
                onClick={() => navigate("/about-us")}
                className="w-full sm:w-auto px-8 sm:px-10 py-3 sm:py-4 border-2 border-gray-300 text-gray-700 text-lg sm:text-xl font-semibold rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 sm:min-w-[250px] bg-white"
              >
                Learn More
              </button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-12 sm:py-16 px-4 sm:px-6 lg:px-8 bg-gray-100">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-8 sm:mb-12">
              <div className="col-span-1 sm:col-span-2 md:col-span-2">
                <img
                  src="/assets/logo.png"
                  alt="CryptoMentor"
                  className="h-10 sm:h-12 w-auto mb-4"
                />
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed max-w-md mb-4 sm:mb-6">
                  CryptoMentor is your AI-powered companion for navigating the complex world of cryptocurrency investments with confidence and intelligence.
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                    <Icon icon="mdi:twitter" className="text-xl sm:text-2xl" />
                  </a>
                  <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                    <Icon icon="mdi:linkedin" className="text-xl sm:text-2xl" />
                  </a>
                  <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                    <Icon icon="mdi:discord" className="text-xl sm:text-2xl" />
                  </a>
                  <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                    <Icon icon="mdi:telegram" className="text-xl sm:text-2xl" />
                  </a>
                </div>
              </div>

              <div>
                <h4 className="text-gray-900 font-semibold mb-3 sm:mb-4 text-sm sm:text-base">Product</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Features</a></li>
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Pricing</a></li>
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">API</a></li>
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Security</a></li>
                </ul>
              </div>

              <div>
                <h4 className="text-gray-900 font-semibold mb-3 sm:mb-4 text-sm sm:text-base">Support</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Help Center</a></li>
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Contact Us</a></li>
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Privacy Policy</a></li>
                  <li><a href="#" className="text-sm sm:text-base text-gray-600 hover:text-gray-900 transition-colors">Terms of Service</a></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-300 pt-6 sm:pt-8 text-center">
              <p className="text-xs sm:text-sm md:text-base text-gray-600">&copy; 2025 CryptoMentor. All rights reserved. Built with ❤️ for crypto investors.</p>
            </div>
          </div>
        </footer>
      </div>

      {/* Scroll Indicator */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-50">
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center bg-white/80 backdrop-blur-sm shadow-sm">
          <div className="w-1 h-3 bg-gray-500 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
