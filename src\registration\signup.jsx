import React, { useState } from "react";
import Header from "../components/header";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { toast } from "react-toastify";
import DisclaimerModal from "../components/disclaimerModal";

const SignUp = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showPassword2, setShowPassword2] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    rePassword: "",
  });
  const [formErrors, setFormErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleModalConfirm = async () => {
    setIsModalOpen(false);
    try {
      setLoading(true);
      const formDataPayload = new FormData();
      formDataPayload.append("email", formData.email);
      formDataPayload.append("password", formData.password);
      formDataPayload.append("confirm_password", formData.rePassword);
      formDataPayload.append("fullname", formData.name);
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/auth/signup`,
        formDataPayload,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      toast.success(response?.data?.Response);
      navigate("/verify-otp", { state: { fromRoute1: true, formData } });
    } catch (error) {
      console.log(error);

      toast.error(err?.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const validate = (values) => {
    const errors = {};
    const regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;

    if (values.name === "") {
      errors.name = "Name is required";
    } else if (values.name.length > 50) {
      errors.name =
        "Username is too long. Please enter a username with fewer than 50 characters";
    } else if (!/^[a-zA-Z\s]+$/.test(values.name)) {
      errors.name = "Invalid characters detected. Please remove symbols.";
    }

    if (values.email === "") {
      errors.email = "Email is required";
    } else if (!regex.test(values.email)) {
      errors.email = "This is not a valid Email Format";
    }

    if (values.password === "") {
      errors.password = "Password is required";
    } else if (!passwordRegex.test(values.password)) {
      errors.password =
        "Password must be at least 8 characters long and contain a mix of letters, numbers, and symbols";
    }

    if (values.rePassword === "") {
      errors.rePassword = "Re-enter Password is required";
    } else if (values.password !== values.rePassword) {
      errors.rePassword = "Passwords do not match. Please try again.";
    }

    return errors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const errors = validate(formData);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      Object.keys(errors).forEach((key) => {
        toast.error(errors[key]);
      });
      return;
    }

    setIsModalOpen(true);
  };
  return (
    <>
      <Header />

      <div className="login-main md:!p-0 ">
        <div className="login-main-illustration">
          <img src="/assets/illustration.png" alt="illustration" />
        </div>
        <div className="signin-form !p-0 !max-w-[450px]">
          <h2 className="signin-title !mt-0 !text-xl">
            Create Your Account and Start Chatting
          </h2>
          <p className="signin-subtitle !text-sm ">
            It's quick and easy to get started.
          </p>
          <form onSubmit={handleSubmit} className="">
            <div className="my-5">
              <div className="form-group ">
                <label htmlFor="email" className="form-label">
                  Enter your Full name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  placeholder="Full Name"
                  className="form-input"
                  value={formData.name}
                  onChange={handleChange}
                />
                <label htmlFor="email" className="form-label">
                  Enter your Email address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Email Address"
                  className="form-input"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Enter your Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Password"
                    className="form-input"
                    value={formData.password}
                    onChange={handleChange}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? (
                      <img src="/assets/eye.svg" alt="Show" />
                    ) : (
                      <img src="/assets/eye-close.svg" alt="Hide" />
                    )}
                  </button>
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Confirm your Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="rePassword"
                    name="rePassword"
                    placeholder="Re-enter Password"
                    className="form-input"
                    value={formData.rePassword}
                    onChange={handleChange}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword2(!showPassword2)}
                    className="password-toggle"
                  >
                    {showPassword ? (
                      <img src="/assets/eye.svg" />
                    ) : (
                      <img src="/assets/eye-close.svg" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="form-group-links">
              <button
                type="submit"
                className="signin-button"
                disabled={loading}
              >
                {loading ? "Signing Up..." : "Sign Up"}
              </button>
              <p className="sigin-form-signup-link">
                Already have an account?{" "}
                <span
                  className="cursor-pointer"
                  onClick={() => navigate("/signin")}
                >
                  Sign in
                </span>
              </p>
            </div>
          </form>
        </div>
      </div>
      <DisclaimerModal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        handleAcknowledge={handleModalConfirm}
      />
    </>
  );
};

export default SignUp;
