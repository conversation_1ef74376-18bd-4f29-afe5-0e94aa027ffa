import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import secureLocalStorage from "react-secure-storage";
import RecommendationMethodology from "../components/RecommendationMethodology";

const AboutUs = () => {
  const navigate = useNavigate();
  const userData = secureLocalStorage.getItem("userData");
  const [showMethodology, setShowMethodology] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Back Button */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button 
              onClick={() => navigate(-1)} 
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <img src="assets/Back.svg" alt="Back" className="w-6 h-6 mr-2" />
              <span className="hidden sm:inline">Back</span>
            </button>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
              About CryptoMentor
            </h1>
            <div className="w-8"></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 sm:p-8 mb-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="mb-6">
              <div className="text-4xl sm:text-5xl mb-4">🚀</div>
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4">
                Your Intelligent Crypto Investment Companion
              </h2>
            </div>
            
            <div className="space-y-4 text-gray-600 leading-relaxed text-left sm:text-center">
              <p>
                CryptoMentor is an intelligent investment companion designed to
                revolutionize how you approach the crypto market. Powered by
                cutting-edge AI, CryptoMentor synthesizes complex market signals, such
                as technical analysis, on-chain data, fundamental analysis, social
                media trends, and the fear/greed index, into actionable
                recommendations tailored to your unique investment profile.
              </p>
              <p>
                Whether you're a new investor taking your first steps into crypto or a
                seasoned trader looking for smarter strategies, CryptoMentor makes
                investing simpler, more informed, and aligned with your financial
                goals. Our platform evaluates your risk tolerance, income, and
                investment capital to create personalized portfolio strategies, so you
                can make confident decisions in an ever-evolving market.
              </p>
              <p>
                With CryptoMentor, you don't just invest—you invest intelligently.
                Navigate the crypto market with confidence and start your journey
                today.
              </p>
            </div>

            {!userData && (
              <div className="mt-8 space-y-4">
                <p className="text-sm text-gray-600">
                  Sign up for CryptoMentor and unlock the power of smart
                  AI-powered investing!
                </p>
                <button
                  onClick={() => navigate("/signup")}
                  className="bg-black text-white px-8 py-3 rounded-lg shadow-md hover:bg-gray-700 transition-colors font-medium"
                >
                  Sign Up Now
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Key Features Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 sm:p-8 mb-8">
          <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-6 text-center">
            🎯 What Makes CryptoMentor Different
          </h3>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center p-4">
              <div className="text-3xl mb-3">🤖</div>
              <h4 className="font-semibold text-gray-800 mb-2">AI-Powered Analysis</h4>
              <p className="text-sm text-gray-600">
                Advanced algorithms analyze multiple data sources to provide intelligent recommendations
              </p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl mb-3">🎯</div>
              <h4 className="font-semibold text-gray-800 mb-2">Personalized Advice</h4>
              <p className="text-sm text-gray-600">
                Tailored recommendations based on your risk tolerance and investment goals
              </p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl mb-3">🛡️</div>
              <h4 className="font-semibold text-gray-800 mb-2">Risk Protection</h4>
              <p className="text-sm text-gray-600">
                Advanced rug pull detection and comprehensive risk assessment
              </p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl mb-3">💬</div>
              <h4 className="font-semibold text-gray-800 mb-2">Conversational AI</h4>
              <p className="text-sm text-gray-600">
                Natural dialogue with context-aware follow-up questions and clarifications
              </p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl mb-3">📊</div>
              <h4 className="font-semibold text-gray-800 mb-2">Real-Time Data</h4>
              <p className="text-sm text-gray-600">
                Live market data, social sentiment, and technical analysis
              </p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl mb-3">🔍</div>
              <h4 className="font-semibold text-gray-800 mb-2">Transparent Process</h4>
              <p className="text-sm text-gray-600">
                Clear explanations of how recommendations are generated
              </p>
            </div>
          </div>
        </div>

        {/* Methodology Toggle Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 sm:p-8 mb-8">
          <div className="text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4">
              🧠 How Our Algorithm Works
            </h3>
            <p className="text-gray-600 mb-6 max-w-3xl mx-auto">
              Curious about how CryptoMentor generates its recommendations? 
              Explore our transparent methodology and see exactly how we analyze 
              the market to provide you with intelligent investment advice.
            </p>
            
            <button
              onClick={() => setShowMethodology(!showMethodology)}
              className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-8 py-3 rounded-lg shadow-md hover:from-purple-600 hover:to-blue-600 transition-all font-medium flex items-center mx-auto"
            >
              <span className="mr-2">
                {showMethodology ? "Hide" : "Explore"} Our Methodology
              </span>
              <span className={`transform transition-transform ${showMethodology ? "rotate-180" : ""}`}>
                ▼
              </span>
            </button>
          </div>
        </div>

        {/* Expandable Methodology Section */}
        <div className={`transition-all duration-500 ease-in-out overflow-hidden ${
          showMethodology ? "max-h-none opacity-100" : "max-h-0 opacity-0"
        }`}>
          {showMethodology && (
            <div className="mb-8">
              <RecommendationMethodology />
            </div>
          )}
        </div>

        {/* Trust & Security Section */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 sm:p-8 border border-green-200">
          <div className="text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4">
              🔒 Trust & Transparency
            </h3>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
              <div className="text-center">
                <div className="text-2xl mb-2">🔍</div>
                <h4 className="font-semibold text-gray-800 mb-1">Open Algorithm</h4>
                <p className="text-sm text-gray-600">
                  Full transparency in how we generate recommendations
                </p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">🛡️</div>
                <h4 className="font-semibold text-gray-800 mb-1">Risk First</h4>
                <p className="text-sm text-gray-600">
                  Comprehensive risk assessment protects your investments
                </p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">📊</div>
                <h4 className="font-semibold text-gray-800 mb-1">Data Driven</h4>
                <p className="text-sm text-gray-600">
                  Decisions based on real market data, not speculation
                </p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">🎯</div>
                <h4 className="font-semibold text-gray-800 mb-1">Your Goals</h4>
                <p className="text-sm text-gray-600">
                  Recommendations aligned with your personal objectives
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action (for non-logged in users) */}
        {!userData && (
          <div className="bg-black text-white rounded-lg p-6 sm:p-8 text-center mt-8">
            <h3 className="text-xl sm:text-2xl font-bold mb-4">
              Ready to Start Your Intelligent Crypto Journey?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of investors who trust CryptoMentor to guide their 
              crypto investment decisions with AI-powered intelligence.
            </p>
            <button
              onClick={() => navigate("/signup")}
              className="bg-white text-black px-8 py-3 rounded-lg shadow-md hover:bg-gray-100 transition-colors font-medium"
            >
              Get Started Today
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AboutUs;