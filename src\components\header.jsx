import React, { useContext, useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import secureLocalStorage from "react-secure-storage";
import { AppContext } from "../Context/AppContext";
import { Icon } from "@iconify/react/dist/iconify.js";
import UpdateUserInfo from "./updateUserinfo";

const Header = () => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [showModal , setShowModal] = useState(false)
  const [showThemeSelector, setShowThemeSelector] = useState(false);
  const {setChatHistory, resetInactivityTimer, isMemeMode, toggleMemeMode, memeTheme, memeThemes, changeMemeTheme} = useContext(AppContext);

  const userData = secureLocalStorage.getItem("userData");
  const { logout } = useContext(AppContext);

  const profileRef = useRef(null); // Reference to the profile avatar div
  const modalRef = useRef(null); // Reference to the modal div
  const themeSelectorRef = useRef(null); // Reference to the theme selector


  const handleOpenModal = () => {
    // Reset inactivity timer when user opens modal
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }
    setIsOpen(true);
  };

  const handleCloseModal = () => {
    // Reset inactivity timer when user closes modal
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }
    setIsOpen(false);
  };



  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target) &&
        profileRef.current &&
        !profileRef.current.contains(event.target)
      ) {
        handleCloseModal();
      }

      // Close theme selector when clicking outside
      if (
        themeSelectorRef.current &&
        !themeSelectorRef.current.contains(event.target)
      ) {
        setShowThemeSelector(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Always listen for theme selector clicks
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const getModalClasses = () => {
    if (profileRef.current) {
      return `absolute z-50 bg-white shadow-lg rounded-lg transition-all duration-200 transform`;
    }
    return "hidden";
  };

  return (
    <div className="relative flex justify-between px-2  lg:px-6 items-center">
      <div className={`hidden md:flex w-full justify-center lg:w-auto lg:block ${userData ? "pt-0 ml-12": "pt-12 "} `}>
        <a href="/">
          <img
            className={`w-52 transition-all duration-500 ${
              isMemeMode ? 'animate-float filter drop-shadow-lg' : ''
            }`}
            src="/assets/logo.png"
            alt="logo"
          />
        </a>
      </div>

      {/* Meme Mode Toggle */}
      {userData && (
        <div className="flex flex-1 md:flex-none ml-auto items-center justify-center md:justify-start space-x-3">
          <span className={`text-sm font-medium transition-all duration-300 hidden sm:inline-block ${
            isMemeMode
              ? 'text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 animate-pulse'
              : 'text-gray-600'
          }`}>
            🚀 Meme Mode
          </span>
          <button
            onClick={toggleMemeMode}
            className={`relative inline-flex h-8 w-16 items-center rounded-full transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              isMemeMode
                ? 'bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-lg shadow-purple-500/50 animate-pulse'
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
          >
            <span
              className={`inline-block h-6 w-6 transform rounded-full bg-white transition-all duration-500 shadow-lg ${
                isMemeMode ? 'translate-x-9 rotate-180 scale-110' : 'translate-x-1'
              }`}
            >
              <span className={`flex items-center justify-center h-full text-xs transition-all duration-300 ${
                isMemeMode ? 'animate-bounce' : ''
              }`}>
                {isMemeMode ? '🎉' : '😴'}
              </span>
            </span>
            {/* Sparkle effects when meme mode is on */}
            {isMemeMode && (
              <>
                <span className="absolute -top-1 -left-1 text-xs animate-ping">✨</span>
                <span className="absolute -bottom-1 -right-1 text-xs animate-ping delay-75">⭐</span>
                <span className="absolute -top-1 -right-1 text-xs animate-ping delay-150">💫</span>
              </>
            )}
          </button>

          {/* Theme Color Selector - Only show when meme mode is active */}
          {isMemeMode && (
            <div ref={themeSelectorRef} className="relative">
              <button
                onClick={() => setShowThemeSelector(!showThemeSelector)}
                className="flex items-center space-x-1 px-3 py-2 bg-white/20 backdrop-blur-sm rounded-full border border-white/30 hover:bg-white/30 transition-all duration-300"
              >
                <span className="text-lg">{memeThemes[memeTheme]?.emoji}</span>
                <span className="text-xs text-white font-medium hidden sm:block">Theme</span>
                <Icon icon="mdi:chevron-down" className={`text-white transition-transform duration-300 ${showThemeSelector ? 'rotate-180' : ''}`} />
              </button>

              {/* Theme Selector Dropdown */}
              {showThemeSelector && (
                <div className="absolute top-full right-0 mt-2 bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 shadow-2xl p-3 min-w-[200px] z-50">
                  <div className="text-xs text-white/80 mb-2 font-medium">Choose Theme:</div>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(memeThemes).map(([key, theme]) => (
                      <button
                        key={key}
                        onClick={() => {
                          changeMemeTheme(key);
                          setShowThemeSelector(false);
                        }}
                        className={`flex items-center space-x-2 p-2 rounded-lg transition-all duration-300 hover:scale-105 ${
                          memeTheme === key
                            ? 'bg-white/30 ring-2 ring-white/50'
                            : 'bg-white/10 hover:bg-white/20'
                        }`}
                      >
                        <span className="text-lg">{theme.emoji}</span>
                        <span className="text-xs text-white font-medium">{theme.name.split(' ')[1]}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
      <div className="ml-auto flex items-center gap-2 sm:gap-3 flex-wrap sm:flex-nowrap">
        {!userData && (
          <button
            onClick={() => navigate("/about-us")}
            className="bg-black text-white py-1 px-2 text-[11px] sm:text-sm sm:px-3 rounded-md shadow-md hover:bg-gray-600 transition whitespace-nowrap"
            aria-label="About Us"
          >
            <span className="sm:hidden flex items-center justify-center">
              <Icon icon="mdi:information-outline" fontSize={20} />
            </span>
            <span className="hidden sm:inline">About Us</span>
          </button>
        )}
        {!userData && (
          <button
            onClick={() => navigate("/signup")}
            className="bg-black text-white py-1 px-2 text-[11px] sm:text-sm sm:px-3 rounded-md shadow-md hover:bg-gray-600 transition whitespace-nowrap"
          >
            Join Us
          </button>
        )}
        {userData && (
          <div
            ref={profileRef}
            onClick={handleOpenModal}
            className={`flex items-center rounded-xl px-2 cursor-pointer transition-all duration-300 flex-shrink-0 ${
              isMemeMode ? 'hover:scale-110' : ''
            }`}
          >
            <img
              src="/assets/avatar.png"
              alt="Avatar"
              className={`w-10 h-10 rounded-full transition-all duration-300 ${
                isMemeMode ? 'animate-bounce border-2 border-white shadow-lg' : ''
              }`}
            />
            <div className="ml-2">
              <h2 className={`font-medium hidden lg:block transition-all duration-300 ${
                isMemeMode
                  ? 'text-white text-shadow-lg animate-pulse'
                  : 'text-black'
              }`}>
                {userData?.fullname || "username"}
              </h2>
            </div>
          </div>
        )}
      </div>

      {isOpen && (
        <div
          ref={modalRef}
          className={`${getModalClasses()}`}
          style={{
            top: profileRef.current.offsetTop + profileRef.current.offsetHeight,
            right:  0,
          }}
        >
          <div className="flex flex-col items-start p-4 space-y-2">
            {userData && (
              <>
                <h2 className="text-black font-medium block lg:hidden">
                  {userData?.fullname || "username"}
                </h2>
                <button
                  className="w-full flex items-center gap-2 text-sm text-gray-600 hover:text-blue-500"
                  onClick={() => {
                    setShowModal(true)
                  }}
                >
                  <Icon icon="solar:user-linear" fontSize={18} />
                  Update info
                </button>
                <button
                  className="w-full flex items-center gap-2 text-sm text-gray-600 hover:text-red-500"
                  onClick={() => setChatHistory([])}
                >
                  <Icon icon="mdi:trash-can-outline" fontSize={18} />
                  Clear all conversation
                </button>

                <button
                  className="w-full flex items-center gap-2 text-sm text-gray-600 hover:text-blue-500"
                  onClick={() => {
                    navigate("feedback");
                  }}
                >
                  <Icon icon="mdi:message-text-outline" fontSize={18} />
                  Feedback
                </button>
                <button
                  className="w-full flex items-center gap-2 text-sm text-gray-600 hover:text-blue-500"
                  onClick={() => {
                    navigate("about-us");
                  }}
                >
                  <Icon icon="mdi:information-outline" fontSize={18} />
                  About Us
                </button>

                <button
                  className="w-full flex items-center gap-2 text-sm text-red-600 hover:text-red-700"
                  onClick={() => {
                    // Implement account deletion logic here
                    // This will likely involve a confirmation step and an API call
                    console.log("Delete account clicked");
                    // For now, just close the modal
                    handleCloseModal();
                  }}
                >
                  <Icon icon="mdi:delete" fontSize={18} />
                  Delete Account
                </button>

                <button
                  className="w-full flex items-center gap-2 text-sm text-red-600 hover:text-red-700"
                  onClick={() => {
                    logout();
                    navigate("/signin");
                  }}
                >
                  <Icon icon="mdi:logout" fontSize={18} />
                  Log out
                </button>
              </>
            )}
          </div>
        </div>
      )}
      <UpdateUserInfo  showModal={showModal} setShowModal={setShowModal} />
    </div>
  );
};

export default Header;
