import React, { useState } from "react";
import Header from "../components/header";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { toast } from "react-toastify";

const VerifyEmail = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
  });

  const validateEmail = (email) => {
    const re = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    return re.test(String(email).toLowerCase());
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const { email } = formData;

    if (!email) {
      toast.error("Email is required");
      return;
    } else if (!validateEmail(email)) {
      toast.error("Invalid email address");
      return;
    }
    const formDataPayload = new FormData();
    formDataPayload.append("email", formData.email);
    setLoading(true);
    axios
      .post(
        `${import.meta.env.VITE_API_URL}/auth/forget-password`,
        formDataPayload,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      )
      .then((res) => {
        toast.success(res?.data?.message);
        navigate("/verify-otp", { state: { fromRoute2: true, email } });
      })
      .catch((err) => {
        toast.error(err?.response?.data?.message || "Something went wrong");
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <>
      <Header />

      <div className="login-main">
        <div className="login-main-illustration">
          <img src="/assets/illustration.png" alt="illustration" />
        </div>
        <div className="signin-form">
          <h2 className="signin-title">
            Enter your Email to receive the Verification Code
          </h2>
          <p className="signin-subtitle">
            you will recive the OTP on this email
          </p>
          <form onSubmit={handleSubmit} className="">
            <div className="my-10">
              <div className="form-group ">
                <label htmlFor="email" className="form-label">
                  Enter your Email address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Email address"
                  className="form-input"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              {/* <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Enter your Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Password"
                    className="form-input"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? (
                      <img src="/assets/eye.svg" />
                    ) : (
                      <img src="/assets/eye-close.svg" />
                    )}
                  </button>
                </div>
              </div> */}
            </div>
            <div className="form-group-links">
              {/* <p className="forgot-password">Forgot Password?</p> */}

              <button
                type="submit"
                className="signin-button"
                disabled={loading}
              >
                {loading ? "Sending..." : "Verify Email"}
              </button>
              <p className="sigin-form-signup-link">
                go to sign in{" "}
                <span
                  onClick={() => navigate("/signin")}
                  className="cursor-pointer"
                >
                  Sign in
                </span>
              </p>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default VerifyEmail;
