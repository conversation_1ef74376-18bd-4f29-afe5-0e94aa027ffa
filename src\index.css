@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --bg-color: #fff5f8;
  --text-color: #000000;
  --card-bg: #ffffff;
  --border-color: #e5e7eb;
}

.meme-mode {
  --bg-color: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
  --text-color: #ffffff;
  --card-bg: rgba(255, 255, 255, 0.1);
  --border-color: rgba(255, 255, 255, 0.2);
}

body {
  font-family: "Poppins", sans-serif;
  background: var(--bg-color) !important;
  background-size: 400% 400% !important;
  animation: rainbow 10s ease infinite;
  transition: all 0.5s ease;
  /* overflow: hidden; */
}

.meme-mode body {
  animation: rainbow 10s ease infinite;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 5px; /* Width of the scrollbar */
}

::-webkit-scrollbar-track {
  background: white; /* Track color */
}
.typing-indicator {
  display: inline-block;
  width: 60px;
  height: 10px;
  border-radius: 5px;
  background: #ddd;
  position: relative;
}

.typing-indicator::before,
.typing-indicator::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.5s infinite ease-in-out;
}

.typing-indicator::before {
  left: 0;
  animation-delay: 0s;
}

.typing-indicator::after {
  right: 0;
  animation-delay: 0.75s;
}

@keyframes typing {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}
::-webkit-scrollbar-thumb {
  background-color: #979797; /* Scrollbar color */
  border-radius: 10px; /* Rounded corners */
}

::-webkit-scrollbar-thumb:hover {
  background: #555; /* Darker color on hover */
}

.ant-modal-footer {
  display: none;
}

.ant-modal-title {
  font-size: 22px !important;
  font-weight: 700;
  text-align: center;
  color: #2b2b2b;
  font-family: "Lato";
}

/* Global CSS for customizing the Ant Design Modal close icon */
.ant-modal-close {
  margin-right: 28px; /* Replace 10rem with your desired mx-40 equivalent */
  margin-top: 10px;
}
@media (max-width: 640px) {
  .ant-modal-close {
    margin-right: 0;
  }
}
.ant-modal-close-x {
  color: #0085ff; /* Optional: Set icon color if needed */
  background-image: url("./assets/icons/closeIcon.svg"); /* Path to your custom SVG icon */
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  font-size: 0 !important; /* Hide the default X icon */
  width: 26px; /* Set width and height to match your SVG */
  height: 26px;
}

.ant-modal-body label,
input,
button {
  font-weight: 400 !important;
}

.error-message {
  color: red;
  font-size: 14px;
}

/* Meme Mode Specific Styles */
.meme-mode .sidebar-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  animation: float 3s ease-in-out infinite;
}

.meme-mode .chat-message {
  animation: wiggle 0.5s ease-in-out;
}

.meme-mode .logo {
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
}

.meme-mode .profile-avatar {
  animation: bounce 2s infinite;
}

.meme-mode .button {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) !important;
  background-size: 400% 400% !important;
  animation: rainbow 3s ease infinite !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3) !important;
}

.meme-mode .text-primary {
  color: #ffffff !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.meme-mode .bg-white {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

@keyframes rainbow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

/* Theme-specific styles */
.meme-theme-purple {
  --theme-primary: linear-gradient(to right, #8b5cf6, #ec4899);
  --theme-secondary: linear-gradient(to right, #a78bfa, #f472b6);
  --theme-background: linear-gradient(to bottom right, #a78bfa, #ec4899, #ef4444);
  --theme-accent: #8b5cf6;
}

.meme-theme-ocean {
  --theme-primary: linear-gradient(to right, #3b82f6, #06b6d4);
  --theme-secondary: linear-gradient(to right, #60a5fa, #22d3ee);
  --theme-background: linear-gradient(to bottom right, #60a5fa, #06b6d4, #14b8a6);
  --theme-accent: #3b82f6;
}

.meme-theme-sunset {
  --theme-primary: linear-gradient(to right, #f97316, #ef4444);
  --theme-secondary: linear-gradient(to right, #fb923c, #f87171);
  --theme-background: linear-gradient(to bottom right, #fb923c, #ef4444, #ec4899);
  --theme-accent: #f97316;
}

.meme-theme-galaxy {
  --theme-primary: linear-gradient(to right, #6366f1, #8b5cf6);
  --theme-secondary: linear-gradient(to right, #818cf8, #a78bfa);
  --theme-background: linear-gradient(to bottom right, #818cf8, #8b5cf6, #ec4899);
  --theme-accent: #6366f1;
}

.meme-theme-neon {
  --theme-primary: linear-gradient(to right, #22c55e, #eab308);
  --theme-secondary: linear-gradient(to right, #4ade80, #facc15);
  --theme-background: linear-gradient(to bottom right, #4ade80, #eab308, #f97316);
  --theme-accent: #22c55e;
}

.meme-theme-fire {
  --theme-primary: linear-gradient(to right, #ef4444, #eab308);
  --theme-secondary: linear-gradient(to right, #f87171, #facc15);
  --theme-background: linear-gradient(to bottom right, #f87171, #f97316, #eab308);
  --theme-accent: #ef4444;
}

