import React, { useEffect, useState } from 'react';
import axios from 'axios';

const CredibleNews = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCredibleNews = async () => {
      try {
        setLoading(true);
        const apiKey = "7756c37abc57433490c1759d648195c0";
        if (!apiKey) {
          throw new Error("News API key is not defined in environment variables.");
        }

        // Define credible sources. NewsAPI requires source IDs for the 'sources' parameter.
        // For a comprehensive list, one would typically check NewsAPI documentation.
        // For this example, I'll use a few common ones.
        // Note: NewsAPI's free tier might have limitations on sources.
        const credibleSources = "bbc-news,cnn,nbc-news,reuters,associated-press,the-washington-post,the-new-york-times,bloomberg,wall-street-journal";
        const response = await axios.get(
          `https://newsapi.org/v2/top-headlines?q=crypto&apiKey=${apiKey}`
        );
        const cryptoArticles = response.data.articles.filter(article =>
          (article.title && article.title.toLowerCase().includes('crypto')) ||
          (article.description && article.description.toLowerCase().includes('crypto'))
        );
        console.log(cryptoArticles);
        setNews(cryptoArticles);
      } catch (err) {
        console.error("Error fetching credible news:", err);
        setError("Failed to load credible news. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchCredibleNews();
  }, []);

  if (loading) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Credible Crypto News</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="animate-pulse">
                <div className="bg-gray-200 h-48 w-full"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-32"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 text-red-700 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Credible Crypto News</h3>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg shadow-md mt-4">
      <h3 className="text-lg font-semibold mb-4">Credible Crypto News</h3>
      {news.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
          {news.slice(0, 5).map((article, index) => (
            <div 
              key={index} 
              className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="aspect-video bg-gray-100 overflow-hidden">
                {article.urlToImage ? (
                  <img 
                    src={article.urlToImage} 
                    alt={article.title} 
                    className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                  />
                ) : (
                  <div className="bg-gray-200 border-2 border-dashed rounded-xl w-full h-full flex items-center justify-center text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
              </div>
              <div className="p-4">
                <h4 className="font-semibold text-lg mb-2">{article.title}</h4>
                <p className="text-gray-600 text-sm mb-4">{article.source.name}</p>
                <a
                  href={article.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Read More
                </a>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-600">No credible crypto news available at the moment.</p>
      )}
    </div>
  );
};

export default CredibleNews;
