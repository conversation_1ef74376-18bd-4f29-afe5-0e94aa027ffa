import React, { useState } from "react";

// Main Recommendation Methodology Component
const RecommendationMethodology = () => {
  const [activeSection, setActiveSection] = useState("overview");

  const sections = [
    { id: "overview", title: "Overview", icon: "📊" },
    { id: "algorithm", title: "Algorithm Flow", icon: "🔄" },
    { id: "datasources", title: "Data Sources", icon: "📡" },
    { id: "risk", title: "Risk Assessment", icon: "⚠️" },
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-8 text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">
          🧠 Our Recommendation Methodology
        </h2>
        <p className="text-gray-600 leading-relaxed max-w-3xl mx-auto">
          Discover how CryptoMentor's AI-powered algorithm analyzes market data, 
          assesses risks, and generates personalized meme coin recommendations 
          tailored to your investment profile.
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex flex-wrap justify-center mb-8 border-b border-gray-200">
        {sections.map((section) => (
          <button
            key={section.id}
            onClick={() => setActiveSection(section.id)}
            className={`px-6 py-3 mx-2 mb-2 rounded-t-lg font-medium transition-colors ${
              activeSection === section.id
                ? "bg-purple-500 text-white border-b-2 border-purple-500"
                : "text-gray-600 hover:text-purple-500 hover:bg-purple-50"
            }`}
          >
            <span className="mr-2">{section.icon}</span>
            {section.title}
          </button>
        ))}
      </div>

      {/* Content Sections */}
      <div className="min-h-[500px]">
        {activeSection === "overview" && <MethodologyOverview />}
        {activeSection === "algorithm" && <AlgorithmFlowchart />}
        {activeSection === "datasources" && <DataSourcesExplanation />}
        {activeSection === "risk" && <RiskAssessmentCriteria />}
      </div>

      {/* Transparency Disclaimer */}
      <TransparencyDisclaimer />
    </div>
  );
};

// Overview Section Component
const MethodologyOverview = () => {
  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-200">
        <h3 className="text-2xl font-bold text-gray-800 mb-4">
          🎯 How Our Algorithm Works
        </h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-lg font-semibold text-purple-700 mb-3">
              🔍 Multi-Factor Analysis
            </h4>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Real-time market data analysis
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Social sentiment monitoring
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Technical indicator evaluation
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Risk pattern recognition
              </li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-semibold text-blue-700 mb-3">
              🎯 Personalized Recommendations
            </h4>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Risk tolerance matching
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Investment capital consideration
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Experience level adaptation
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Goal-oriented strategies
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="text-3xl mb-3">⚡</div>
          <h4 className="text-lg font-semibold text-gray-800 mb-2">
            Real-Time Processing
          </h4>
          <p className="text-gray-600 text-sm">
            Our algorithm processes market data in real-time, ensuring 
            recommendations are based on the latest market conditions.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="text-3xl mb-3">🛡️</div>
          <h4 className="text-lg font-semibold text-gray-800 mb-2">
            Risk Protection
          </h4>
          <p className="text-gray-600 text-sm">
            Advanced rug pull detection and risk assessment algorithms 
            protect you from potentially dangerous investments.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="text-3xl mb-3">🎯</div>
          <h4 className="text-lg font-semibold text-gray-800 mb-2">
            Precision Targeting
          </h4>
          <p className="text-gray-600 text-sm">
            Specific entry points, profit targets, and stop-loss levels 
            are calculated for each recommendation.
          </p>
        </div>
      </div>
    </div>
  );
};

// Algorithm Flowchart Component with Interactive Mermaid-style Diagram
const AlgorithmFlowchart = () => {
  const [selectedStep, setSelectedStep] = useState(null);

  const flowSteps = [
    {
      id: "input",
      title: "User Input",
      description: "Investment profile, risk tolerance, and query analysis",
      color: "bg-blue-500",
      position: { x: 50, y: 10 }
    },
    {
      id: "market",
      title: "Market Data Collection",
      description: "Real-time price, volume, and market cap data gathering",
      color: "bg-green-500",
      position: { x: 20, y: 30 }
    },
    {
      id: "social",
      title: "Social Sentiment Analysis",
      description: "Twitter, Reddit, and influencer mention analysis",
      color: "bg-purple-500",
      position: { x: 80, y: 30 }
    },
    {
      id: "risk",
      title: "Risk Assessment",
      description: "Rug pull detection, liquidity analysis, and safety scoring",
      color: "bg-red-500",
      position: { x: 20, y: 60 }
    },
    {
      id: "technical",
      title: "Technical Analysis",
      description: "Chart patterns, indicators, and trend analysis",
      color: "bg-yellow-500",
      position: { x: 80, y: 60 }
    },
    {
      id: "scoring",
      title: "AI Scoring Engine",
      description: "Weighted algorithm combines all factors into recommendation score",
      color: "bg-indigo-500",
      position: { x: 50, y: 80 }
    },
    {
      id: "output",
      title: "Personalized Recommendation",
      description: "Entry points, targets, and risk management advice",
      color: "bg-pink-500",
      position: { x: 50, y: 95 }
    }
  ];

  const connections = [
    { from: "input", to: "market" },
    { from: "input", to: "social" },
    { from: "market", to: "risk" },
    { from: "social", to: "technical" },
    { from: "risk", to: "scoring" },
    { from: "technical", to: "scoring" },
    { from: "scoring", to: "output" }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          🔄 Algorithm Processing Flow
        </h3>
        <p className="text-gray-600">
          Click on any step to learn more about that part of our process
        </p>
      </div>

      {/* Interactive Flowchart */}
      <div className="relative bg-gradient-to-br from-gray-50 to-blue-50 p-8 rounded-lg border border-gray-200 min-h-[500px]">
        <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
          {connections.map((conn, index) => {
            const fromStep = flowSteps.find(s => s.id === conn.from);
            const toStep = flowSteps.find(s => s.id === conn.to);
            return (
              <line
                key={index}
                x1={`${fromStep.position.x}%`}
                y1={`${fromStep.position.y}%`}
                x2={`${toStep.position.x}%`}
                y2={`${toStep.position.y}%`}
                stroke="#9CA3AF"
                strokeWidth="2"
                strokeDasharray="5,5"
                className="animate-pulse"
              />
            );
          })}
        </svg>

        {flowSteps.map((step) => (
          <div
            key={step.id}
            className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-300 ${
              selectedStep === step.id ? 'scale-110 z-20' : 'z-10'
            }`}
            style={{ 
              left: `${step.position.x}%`, 
              top: `${step.position.y}%` 
            }}
            onClick={() => setSelectedStep(selectedStep === step.id ? null : step.id)}
          >
            <div className={`${step.color} text-white p-4 rounded-lg shadow-lg min-w-[150px] text-center hover:shadow-xl transition-shadow`}>
              <div className="font-semibold text-sm mb-1">{step.title}</div>
              {selectedStep === step.id && (
                <div className="text-xs mt-2 bg-white bg-opacity-20 p-2 rounded">
                  {step.description}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Step Details */}
      {selectedStep && (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h4 className="text-lg font-semibold text-gray-800 mb-3">
            {flowSteps.find(s => s.id === selectedStep)?.title} - Detailed Process
          </h4>
          <div className="text-gray-600">
            {selectedStep === "input" && (
              <div>
                <p className="mb-3">The algorithm begins by analyzing your investment profile:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Risk tolerance level (Conservative, Moderate, Aggressive)</li>
                  <li>Available investment capital</li>
                  <li>Experience level and trading history</li>
                  <li>Specific query context and follow-up questions</li>
                </ul>
              </div>
            )}
            {selectedStep === "market" && (
              <div>
                <p className="mb-3">Real-time market data collection includes:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Current price and 24h price changes</li>
                  <li>Trading volume and liquidity metrics</li>
                  <li>Market capitalization and token supply</li>
                  <li>Exchange listings and trading pairs</li>
                </ul>
              </div>
            )}
            {selectedStep === "social" && (
              <div>
                <p className="mb-3">Social sentiment analysis covers:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Twitter mentions, engagement, and follower growth</li>
                  <li>Reddit community activity and sentiment scores</li>
                  <li>Influencer mentions and endorsements</li>
                  <li>Overall social media buzz and trending status</li>
                </ul>
              </div>
            )}
            {selectedStep === "risk" && (
              <div>
                <p className="mb-3">Comprehensive risk assessment includes:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Rug pull indicators and contract analysis</li>
                  <li>Liquidity pool depth and lock status</li>
                  <li>Developer team verification and history</li>
                  <li>Token distribution and whale wallet analysis</li>
                </ul>
              </div>
            )}
            {selectedStep === "technical" && (
              <div>
                <p className="mb-3">Technical analysis incorporates:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Price chart patterns and trend analysis</li>
                  <li>Support and resistance levels</li>
                  <li>Volume indicators and momentum signals</li>
                  <li>Moving averages and technical oscillators</li>
                </ul>
              </div>
            )}
            {selectedStep === "scoring" && (
              <div>
                <p className="mb-3">The AI scoring engine processes:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Weighted combination of all data sources</li>
                  <li>Machine learning pattern recognition</li>
                  <li>Historical performance correlation</li>
                  <li>Risk-adjusted return calculations</li>
                </ul>
              </div>
            )}
            {selectedStep === "output" && (
              <div>
                <p className="mb-3">Final recommendations include:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Specific entry price points and timing</li>
                  <li>Profit target levels and exit strategies</li>
                  <li>Stop-loss recommendations for risk management</li>
                  <li>Position sizing based on your risk profile</li>
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Data Sources Explanation Component
const DataSourcesExplanation = () => {
  const [expandedSource, setExpandedSource] = useState(null);

  const dataSources = [
    {
      id: "market",
      name: "Market Data APIs",
      weight: 25,
      icon: "📈",
      color: "bg-blue-500",
      description: "Real-time price, volume, and market capitalization data",
      details: {
        sources: ["CoinGecko API", "CoinMarketCap", "DEX Aggregators", "Blockchain Explorers"],
        updateFrequency: "Every 30-60 seconds",
        dataPoints: ["Price", "Volume", "Market Cap", "Circulating Supply", "Trading Pairs"]
      }
    },
    {
      id: "social",
      name: "Social Media Analytics",
      weight: 20,
      icon: "🐦",
      color: "bg-purple-500",
      description: "Twitter, Reddit, and social platform sentiment analysis",
      details: {
        sources: ["Twitter API", "Reddit API", "Telegram Monitoring", "Discord Analytics"],
        updateFrequency: "Real-time streaming",
        dataPoints: ["Mentions", "Sentiment Score", "Engagement Rate", "Follower Growth", "Influencer Activity"]
      }
    },
    {
      id: "technical",
      name: "Technical Indicators",
      weight: 20,
      icon: "📊",
      color: "bg-green-500",
      description: "Chart patterns, indicators, and technical analysis signals",
      details: {
        sources: ["TradingView", "Custom Algorithms", "Historical Price Data"],
        updateFrequency: "Every 5 minutes",
        dataPoints: ["RSI", "MACD", "Moving Averages", "Support/Resistance", "Volume Indicators"]
      }
    },
    {
      id: "onchain",
      name: "On-Chain Analytics",
      weight: 15,
      icon: "⛓️",
      color: "bg-yellow-500",
      description: "Blockchain transaction data and wallet analysis",
      details: {
        sources: ["Etherscan", "BSCScan", "Polygon Scan", "Custom Indexers"],
        updateFrequency: "Every block (~15 seconds)",
        dataPoints: ["Transaction Volume", "Unique Holders", "Whale Movements", "Contract Interactions"]
      }
    },
    {
      id: "risk",
      name: "Risk Assessment Data",
      weight: 15,
      icon: "🛡️",
      color: "bg-red-500",
      description: "Security audits, rug pull indicators, and safety metrics",
      details: {
        sources: ["Security Audit Reports", "Liquidity Trackers", "Rug Pull Databases"],
        updateFrequency: "Continuous monitoring",
        dataPoints: ["Audit Status", "Liquidity Locks", "Team Verification", "Contract Security"]
      }
    },
    {
      id: "news",
      name: "News & Events",
      weight: 5,
      icon: "📰",
      color: "bg-indigo-500",
      description: "Crypto news, announcements, and market events",
      details: {
        sources: ["CoinDesk", "CoinTelegraph", "Official Announcements", "Press Releases"],
        updateFrequency: "Real-time",
        dataPoints: ["News Sentiment", "Event Impact", "Announcement Timing", "Market Reactions"]
      }
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          📡 Data Sources & Weighting
        </h3>
        <p className="text-gray-600">
          Our algorithm combines multiple data sources with carefully calibrated weights
        </p>
      </div>

      {/* Weight Distribution Visualization */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">
          📊 Data Source Weight Distribution
        </h4>
        <div className="space-y-3">
          {dataSources.map((source) => (
            <div key={source.id} className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 min-w-[200px]">
                <span className="text-xl">{source.icon}</span>
                <span className="font-medium text-gray-700">{source.name}</span>
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                <div
                  className={`${source.color} h-4 rounded-full transition-all duration-1000 ease-out`}
                  style={{ width: `${source.weight}%` }}
                ></div>
                <span className="absolute right-2 top-0 text-xs font-medium text-gray-600 leading-4">
                  {source.weight}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Source Information */}
      <div className="grid md:grid-cols-2 gap-6">
        {dataSources.map((source) => (
          <div
            key={source.id}
            className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => setExpandedSource(expandedSource === source.id ? null : source.id)}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{source.icon}</span>
                <div>
                  <h4 className="font-semibold text-gray-800">{source.name}</h4>
                  <span className="text-sm text-gray-500">Weight: {source.weight}%</span>
                </div>
              </div>
              <span className="text-gray-400">
                {expandedSource === source.id ? "▼" : "▶"}
              </span>
            </div>
            
            <p className="text-gray-600 text-sm mb-3">{source.description}</p>
            
            {expandedSource === source.id && (
              <div className="space-y-3 pt-3 border-t border-gray-100">
                <div>
                  <h5 className="font-medium text-gray-700 mb-1">Data Sources:</h5>
                  <div className="flex flex-wrap gap-1">
                    {source.details.sources.map((src, index) => (
                      <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {src}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <h5 className="font-medium text-gray-700 mb-1">Update Frequency:</h5>
                  <span className="text-sm text-gray-600">{source.details.updateFrequency}</span>
                </div>
                <div>
                  <h5 className="font-medium text-gray-700 mb-1">Key Data Points:</h5>
                  <div className="flex flex-wrap gap-1">
                    {source.details.dataPoints.map((point, index) => (
                      <span key={index} className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                        {point}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Data Quality Assurance */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border border-green-200">
        <h4 className="text-lg font-semibold text-gray-800 mb-3">
          ✅ Data Quality Assurance
        </h4>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl mb-2">🔄</div>
            <h5 className="font-medium text-gray-700 mb-1">Real-Time Updates</h5>
            <p className="text-sm text-gray-600">
              Data is continuously updated to ensure recommendations are based on current market conditions
            </p>
          </div>
          <div className="text-center">
            <div className="text-2xl mb-2">✅</div>
            <h5 className="font-medium text-gray-700 mb-1">Data Validation</h5>
            <p className="text-sm text-gray-600">
              Multiple source cross-validation and anomaly detection prevent bad data from affecting recommendations
            </p>
          </div>
          <div className="text-center">
            <div className="text-2xl mb-2">🎯</div>
            <h5 className="font-medium text-gray-700 mb-1">Accuracy Monitoring</h5>
            <p className="text-sm text-gray-600">
              Continuous monitoring of prediction accuracy and algorithm performance optimization
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Risk Assessment Criteria Component
const RiskAssessmentCriteria = () => {
  const [selectedRiskLevel, setSelectedRiskLevel] = useState("moderate");

  const riskLevels = [
    {
      id: "low",
      name: "Low Risk",
      color: "bg-green-500",
      description: "Conservative investments with established track records",
      criteria: {
        marketCap: "> $10M",
        liquidity: "> $1M locked",
        socialScore: "> 80/100",
        auditStatus: "Fully audited",
        rugPullRisk: "< 5%"
      }
    },
    {
      id: "moderate",
      name: "Moderate Risk",
      color: "bg-yellow-500",
      description: "Balanced risk-reward with good fundamentals",
      criteria: {
        marketCap: "$1M - $10M",
        liquidity: "> $500K locked",
        socialScore: "> 60/100",
        auditStatus: "Partially audited",
        rugPullRisk: "5% - 15%"
      }
    },
    {
      id: "high",
      name: "High Risk",
      color: "bg-red-500",
      description: "Higher potential returns with increased risk",
      criteria: {
        marketCap: "$100K - $1M",
        liquidity: "> $100K locked",
        socialScore: "> 40/100",
        auditStatus: "Basic checks",
        rugPullRisk: "15% - 30%"
      }
    }
  ];

  const riskFactors = [
    {
      category: "Rug Pull Indicators",
      icon: "🚨",
      factors: [
        "Anonymous development team",
        "Unlocked liquidity pools",
        "Excessive token concentration",
        "Suspicious contract code",
        "No audit or failed audit"
      ]
    },
    {
      category: "Liquidity Risks",
      icon: "💧",
      factors: [
        "Low trading volume",
        "Thin order books",
        "Single exchange listing",
        "Impermanent loss potential",
        "Liquidity pool manipulation"
      ]
    },
    {
      category: "Market Risks",
      icon: "📉",
      factors: [
        "High price volatility",
        "Market manipulation",
        "Pump and dump schemes",
        "Regulatory uncertainty",
        "Market sentiment shifts"
      ]
    },
    {
      category: "Technical Risks",
      icon: "⚙️",
      factors: [
        "Smart contract bugs",
        "Blockchain congestion",
        "Oracle manipulation",
        "Flash loan attacks",
        "Governance token risks"
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          ⚠️ Risk Assessment Framework
        </h3>
        <p className="text-gray-600">
          Our comprehensive risk evaluation system protects your investments
        </p>
      </div>

      {/* Risk Level Selector */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">
          🎯 Risk Level Classifications
        </h4>
        <div className="flex flex-wrap justify-center gap-4 mb-6">
          {riskLevels.map((level) => (
            <button
              key={level.id}
              onClick={() => setSelectedRiskLevel(level.id)}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                selectedRiskLevel === level.id
                  ? `${level.color} text-white shadow-lg scale-105`
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {level.name}
            </button>
          ))}
        </div>

        {/* Selected Risk Level Details */}
        {riskLevels.map((level) => (
          selectedRiskLevel === level.id && (
            <div key={level.id} className="bg-gray-50 p-6 rounded-lg">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-4 h-4 ${level.color} rounded-full`}></div>
                <h5 className="text-xl font-semibold text-gray-800">{level.name}</h5>
              </div>
              <p className="text-gray-600 mb-4">{level.description}</p>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(level.criteria).map(([key, value]) => (
                  <div key={key} className="bg-white p-3 rounded border">
                    <div className="font-medium text-gray-700 capitalize mb-1">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </div>
                    <div className="text-sm text-gray-600">{value}</div>
                  </div>
                ))}
              </div>
            </div>
          )
        ))}
      </div>

      {/* Risk Factor Categories */}
      <div className="grid md:grid-cols-2 gap-6">
        {riskFactors.map((category) => (
          <div key={category.category} className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center space-x-3 mb-4">
              <span className="text-2xl">{category.icon}</span>
              <h4 className="text-lg font-semibold text-gray-800">{category.category}</h4>
            </div>
            <ul className="space-y-2">
              {category.factors.map((factor, index) => (
                <li key={index} className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-red-400 rounded-full mr-3 flex-shrink-0"></span>
                  {factor}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* Risk Mitigation Strategies */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg border border-blue-200">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">
          🛡️ Risk Mitigation Strategies
        </h4>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-blue-700 mb-3">Automated Protection</h5>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Real-time rug pull detection algorithms
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Automated stop-loss recommendations
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Liquidity monitoring and alerts
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                Smart contract security verification
              </li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-purple-700 mb-3">User Guidelines</h5>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Position sizing based on risk tolerance
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Diversification recommendations
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Entry and exit timing guidance
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                Regular portfolio rebalancing alerts
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Risk Score Calculation */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">
          🧮 Risk Score Calculation
        </h4>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium text-gray-700">Rug Pull Risk Assessment</span>
            <span className="text-sm text-gray-600">40% weight</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium text-gray-700">Liquidity & Volume Analysis</span>
            <span className="text-sm text-gray-600">25% weight</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium text-gray-700">Technical Security Audit</span>
            <span className="text-sm text-gray-600">20% weight</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium text-gray-700">Market Volatility Index</span>
            <span className="text-sm text-gray-600">15% weight</span>
          </div>
        </div>
        <div className="mt-4 p-4 bg-gradient-to-r from-green-100 to-blue-100 rounded-lg">
          <p className="text-sm text-gray-700">
            <strong>Final Risk Score:</strong> Weighted combination of all factors, 
            normalized to 0-100 scale where 0 is lowest risk and 100 is highest risk.
          </p>
        </div>
      </div>
    </div>
  );
};

// Transparency Disclaimer Component
const TransparencyDisclaimer = () => {
  return (
    <div className="mt-8 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
      <div className="flex items-start space-x-3">
        <span className="text-2xl">⚠️</span>
        <div>
          <h4 className="text-lg font-semibold text-gray-800 mb-3">
            Important Disclaimers & Transparency
          </h4>
          <div className="space-y-3 text-sm text-gray-700">
            <p>
              <strong>Not Financial Advice:</strong> All recommendations are for educational 
              purposes only and should not be considered as financial advice. Always conduct 
              your own research and consult with qualified financial advisors.
            </p>
            <p>
              <strong>Algorithm Limitations:</strong> While our AI processes vast amounts of data, 
              cryptocurrency markets are inherently unpredictable. Past performance does not 
              guarantee future results.
            </p>
            <p>
              <strong>Risk Acknowledgment:</strong> Meme coin investments carry extremely high risk. 
              You may lose your entire investment. Only invest what you can afford to lose completely.
            </p>
            <p>
              <strong>Data Accuracy:</strong> We strive for accuracy but cannot guarantee that all 
              data sources are error-free. Market conditions change rapidly and recommendations 
              may become outdated quickly.
            </p>
            <p>
              <strong>Transparency Commitment:</strong> We are committed to transparency in our 
              methodology while protecting proprietary algorithms that give our users an edge 
              in the market.
            </p>
          </div>
          <div className="mt-4 p-3 bg-white bg-opacity-50 rounded border border-yellow-300">
            <p className="text-xs text-gray-600">
              <strong>Last Updated:</strong> {new Date().toLocaleDateString()} | 
              <strong> Algorithm Version:</strong> 2.1.0 | 
              <strong> Risk Framework:</strong> Enhanced Multi-Factor v3.0
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecommendationMethodology;