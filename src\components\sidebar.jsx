import { Icon } from "@iconify/react";
import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AppContext } from "../Context/AppContext.jsx";
import secureLocalStorage from "react-secure-storage";
import Modal from "./modal.jsx";
import axios from "axios";
import InvestmentModal from "./userInfo.jsx";
import TrendingNews from "./TrendingNews.jsx";
import CredibleNews from "./CredibleNews.jsx";

const Sidebar = ({ isOpen, toggleSidebar }) => {
  const navigate = useNavigate();
  const userData = secureLocalStorage.getItem("userData");
  const userInfo = secureLocalStorage.getItem("userInvestment");
  const { setChatHistory, chatHistory, socketRef, isResponseStreaming, resetInactivityTimer, isMemeMode, sendMemeMessage, setIsResponseStreaming, memeTheme, memeThemes } =
    useContext(AppContext);
  const [showModal, setShowModal] = useState(false);
  const [showInvestmentModal, setShowInvestmentModal] = useState(false);

  const startNewChat = async () => {
    // Reset inactivity timer when user starts a new chat
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }

    if (!userData) {
      setShowModal(true);
      return;
    }
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/chatbot/create-session`
      );
      const sessionId = response.data.session_id;
      secureLocalStorage.setItem("session_id", sessionId);
      secureLocalStorage.setItem("chatHistory");
      setChatHistory([]);
      navigate(`/`);
    } catch (error) {
      console.error("Error creating session:", error);
    }
  };
  const handleFaqMessage = async (faqQuestion) => {
    // Reset inactivity timer when user clicks on an FAQ
    if (resetInactivityTimer) {
      resetInactivityTimer();
    }

    // Allow general FAQs for logged-out users, but require login/info for personalized ones
    const requiresUserInfo = faqQuestion === "Which cryptocurrency should I invest in?";

    if (requiresUserInfo && !userData) {
      setShowModal(true);
      return;
    }

    if (requiresUserInfo && !userInfo) {
      // setShowInvestmentModal(true);
      // return;
    }

    if (
      isResponseStreaming ||
      (chatHistory.length > 0 &&
        chatHistory[chatHistory.length - 1].answer === "")
    )
      return;

    const newMessage = {
      question: faqQuestion,
      answer: "",
      userAvatar: "/assets/User Avatar.svg",
      aiAvatar: "/assets/AI Avatar.svg",
    };

    setChatHistory((prev) => [...prev, newMessage]);
    setIsResponseStreaming(true);

    // Determine user info to send based on login status and question type
    const userInfoData = userData && userInfo ? userInfo.data : {}; // Send user info only if logged in and info available

    // Use meme mode API if meme mode is enabled
    if (isMemeMode) {
      try {
        const memeResponse = await sendMemeMessage(faqQuestion, userInfoData);

        // Update the chat history with the meme response
        setChatHistory((prev) => {
          const updatedChatHistory = [...prev];
          const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];

          updatedChatHistory[updatedChatHistory.length - 1] = {
            ...lastMessage,
            answer: memeResponse.analysis || "🎉 Meme mode response received! 🚀"
          };

          return updatedChatHistory;
        });

        setIsResponseStreaming(false);
      } catch (error) {
        console.error('Meme mode API error:', error);
        // Fallback to regular WebSocket if meme API fails
        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
          socketRef.current.send(
            JSON.stringify({ query: faqQuestion, user_info: userInfoData })
          );
        }
      }
    } else {
      // Use regular WebSocket for normal mode
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.send(
          JSON.stringify({ query: faqQuestion, user_info: userInfoData })
        );
      }
    }
  };
  return (
    <div
      style={{ border: isMemeMode ? "1px solid rgba(255,255,255,0.3)" : "1px solid white" }}
      className={`w-72 p-4 m-4 rounded-2xl flex flex-col transition-all duration-500 sidebar-card ${
        isOpen ? "absolute z-20 inset-0 lg:static" : "hidden lg:flex"
      } h-[calc(100vh-25px)] max-h-screen ${
        isMemeMode
          ? 'bg-white/10 backdrop-blur-lg border-white/20 shadow-2xl'
          : 'bg-white'
      }`}
    >
      {/* Header Section */}
      <div className="mb-4 flex items-center justify-between lg:hidden">
        <h1 className="text-lg font-bold"></h1>
        <button
          className={`lg:hidden transition-all duration-300 ${
            isMemeMode ? 'text-white hover:text-yellow-300 animate-pulse' : 'text-black'
          }`}
          onClick={toggleSidebar}
        >
          <Icon icon="mdi:close" fontSize={24} />
        </button>
      </div>

      {/* Main Content Section */}
      <div className="flex-grow overflow-y-auto pr-1">
        {userData && (
          <button
            className={`w-full text-start p-2 rounded-xl mb-4 transition-all duration-300 transform hover:scale-105 ${
              isMemeMode
                ? `bg-gradient-to-r ${memeThemes[memeTheme]?.primary || 'from-purple-500 to-pink-500'} text-white shadow-lg shadow-${memeThemes[memeTheme]?.accent || 'purple-500'}/30 animate-pulse`
                : 'bg-black text-white hover:bg-gray-800'
            }`}
            onClick={() => {
              startNewChat();
            }}
          >
            <span className="px-4"> ➕ </span> Start a new chat
          </button>
        )}
         {/* Trending Crypto News Section */}
         <TrendingNews />
         {/* <CredibleNews /> */}
        <h4 className={`font-medium mb-2 mt-4 transition-colors duration-300 ${
          isMemeMode ? 'text-white' : 'text-black'
        }`}>F.A.Qs</h4>
        <img className="mb-4" src="/assets/lineBreak.svg" alt="divider" />
        <ul className={`space-y-6 text-sm transition-colors duration-300 ${
          isMemeMode ? 'text-white/80' : 'text-[#808080]'
        }`}>
          <li
            onClick={() => handleFaqMessage("How can I start investing into cryptocurrencies?")}
            className={`flex items-center gap-3 cursor-pointer transition-all duration-300 ${
              isMemeMode
                ? 'hover:text-yellow-300 hover:scale-105'
                : 'hover:text-blue-500'
            }`}
          >
            <Icon icon="ic:round-message" fontSize={20} />
            How can I start investing into cryptocurrencies?
          </li>
          <li
            onClick={() => handleFaqMessage("Is investing into cryptocurrencies safe?")}
            className={`flex items-center gap-3 cursor-pointer transition-all duration-300 ${
              isMemeMode
                ? 'hover:text-yellow-300 hover:scale-105'
                : 'hover:text-blue-500'
            }`}
          >
            <Icon icon="ic:round-message" fontSize={20} />
            Is investing into cryptocurrencies safe?
          </li>
          <li
            onClick={() =>
              handleFaqMessage("Which cryptocurrency should I invest in?")
            }
            className={`flex items-center gap-3 cursor-pointer transition-all duration-300 ${
              isMemeMode
                ? 'hover:text-yellow-300 hover:scale-105'
                : 'hover:text-blue-500'
            }`}
          >
            <Icon icon="ic:round-message" fontSize={20} />
            Which cryptocurrency should I invest in?
          </li>
        </ul>


      </div>

      {/* Footer Section */}
      <div className="mt-auto space-y-4 border-t p-4">
        {!userData && (
          <>
            <button
              className="w-full flex items-center gap-2 text-sm text-gray-600 hover:text-blue-500"
              onClick={() => {
                navigate("about-us");
                toggleSidebar();
              }}
            >
              <Icon icon="mdi:information-outline" fontSize={18} />
              About Us
            </button>
          </>
        )}
      </div>
      <Modal isOpen={showModal} onClose={() => setShowModal(false)} />
      <InvestmentModal
        showModal={showInvestmentModal}
        setShowModal={setShowInvestmentModal}
      />
    </div>
  );
};

export default Sidebar;
