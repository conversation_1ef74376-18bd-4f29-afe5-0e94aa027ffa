{"name": "omnichannel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@fontsource/lato": "^5.1.0", "antd": "^5.21.5", "axios": "^1.7.9", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.3", "react-router-dom": "^6.27.0", "react-secure-storage": "^1.3.2", "react-toastify": "^10.0.6", "sass": "^1.80.1"}, "devDependencies": {"@eslint/js": "^9.11.1", "@iconify/react": "^5.1.0", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.4.8"}}