import React, { useContext, useState } from "react";
import Header from "../components/header";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import axios from "axios";
import secureLocalStorage from "react-secure-storage";
import { AppContext } from "../Context/AppContext";
const Login = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { getUserData } = useContext(AppContext);
  const navigate = useNavigate();
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };
  const startNewChat = async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/chatbot/create-session`
      );
      const sessionId = response.data.session_id;
      secureLocalStorage.setItem("session_id", sessionId);
      navigate(`/`);
    } catch (error) {
      console.error("Error creating session:", error);
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const { email, password } = formData;

    if (!email && !password) {
      toast.error("Please fill the required fields");
      return;
    } else if (!email) {
      toast.error("Invalid email address");
      return;
    }

    if (!password) {
      toast.error("Password is required");
      return;
    }
    const formDataPayload = new FormData();
    formDataPayload.append("email", email);
    formDataPayload.append("password", password);
    setLoading(true);
    axios
      .post(`${import.meta.env.VITE_API_URL}/auth/login`, formDataPayload, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
      .then((res) => {
        secureLocalStorage.setItem("userData", res?.data?.data);
        secureLocalStorage.setItem("authToken", res?.data?.data?.access_token);
        toast.success("Logged in Successfully");
        getUserData();

        startNewChat();
        setFormData({
          email: "",
          password: "",
        });
      })
      .catch((err) => {
        console.log(err);

        toast.error(err?.response?.data?.message || "Something went wrong");
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <>
      <Header />

      <div className="login-main">
        <div className="login-main-illustration">
          <img src="/assets/illustration.png" alt="illustration" />
        </div>
        <div className="signin-form">
          <h2 className="signin-title">Welcome Back!</h2>
          <p className="signin-subtitle">
            Login to continue your conversations <br />
            with AI.
          </p>
          <form className="">
            <div className="my-10">
              <div className="form-group ">
                <label htmlFor="email" className="form-label">
                  Enter your Email address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Email address"
                  className="form-input"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Enter your Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Password"
                    className="form-input"
                    value={formData.password}
                    onChange={handleChange}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? (
                      <img src="/assets/eye.svg" />
                    ) : (
                      <img src="/assets/eye-close.svg" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <p
              className="forgot-password"
              onClick={() => navigate("/verify-forget-password")}
            >
              Forgot Password?
            </p>
            <div className="form-group-links">
              <button
                type="submit"
                onClick={handleSubmit}
                className="signin-button"
                disabled={loading}
              >
                {loading ? "Signing in..." : "Sign in"}
              </button>
              <p className="sigin-form-signup-link">
                No Account?{" "}
                <span
                  onClick={() => navigate("/signup")}
                  className="cursor-pointer"
                >
                  Sign up
                </span>
              </p>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default Login;
