import React from "react";

const DisclaimerModal = ({ isOpen , setIsOpen , handleAcknowledge}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
    <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-2xl h-[90%] overflow-y-auto">
      <h2 className="text-center text-lg font-semibold mb-4 ">
        Legal Disclaimer
      </h2>
      <p className="text-sm text-gray-600 leading-relaxed mb-6 ">
        Crypomentor is an AI-powered platform designed to provide educational
        insights and recommendations for cryptocurrency investing. All
        information provided is for informational purposes only and should not
        be considered financial, investment, or legal advice. Cryptocurrency
        investments carry significant risks, including loss of capital, and may
        not be suitable for all investors.
        <br />
        <br />
        Crypomentor does not guarantee the accuracy, completeness, or
        reliability of the data provided, and users are encouraged to perform
        their own research and consult with a financial advisor before making
        any investment decisions. By using Crypomentor, you agree that the
        platform and its creators are not responsible for any financial losses
        or adverse outcomes resulting from your investment activities.
        <br />
        <br />
        Always invest responsibly and within your means.
      </p>
      <button
        className="bg-black text-white py-2 px-4 rounded-md hover:bg-gray-600 transition w-full"
        onClick={() => handleAcknowledge()}
      >
        I Acknowledge
      </button>
    </div>
  </div>
  );
};

export default DisclaimerModal;