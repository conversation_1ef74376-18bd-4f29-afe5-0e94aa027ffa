.login-main {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 20px;
  height: 100vh;

  &-logo {
    margin-bottom: 20px;

    img {
      width: 150px;
      height: auto;
    }
  }

  &-illustration {
    margin-bottom: 30px;

    img {
      width: 380px;
      height: 420px;
      flex-shrink: 0;
    }
  }

  @media(max-width: 1024px) {
    &-illustration {
      img {
        width: 240px;
        height: 280px;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    // Adjust the breakpoint as needed
    flex-direction: column;
    align-items: center;

    &-illustration {
      display: none; // Hide illustration on phones
    }

    &-logo {
      margin-bottom: 20px;

      img {
        width: 120px; // Adjust size for smaller screens
      }
    }
  }
}

.signin-form {
  padding: 30px;
  width: 100%;
  max-width: 400px;

  .signin-title {
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-weight: 600;
  }

  .signin-subtitle {
    color: #5b5b5b;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 400;
  }

  .form-group {
    margin-bottom: 12px;

    .form-label {
      color: #000;
      font-family: Poppins;
      font-size: 16px;
      font-weight: 400;
    }

    .form-input {
      width: 100%;
      margin: 6px 0;
      padding: 10px;
      height: 57px;
      border-radius: 15px;
      border: 1px solid #fff;
      background: rgba(255, 255, 255, 0.55);
      backdrop-filter: blur(166.5px);

      &::placeholder {
        color: #808080;
        font-family: Poppins;
        font-size: 14px;
        font-weight: 300;
      }

      &:focus {
        border: 1px solid #1e1e1e;
      }
    }

    .password-wrapper {
      position: relative;

      .password-toggle {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
      }
    }
  }

  .form-group-links {
    text-align: center;

    .forgot-password {
      color: #4285f4;
      text-align: end;
      font-family: Poppins;
      font-size: 13px;
      margin: 5px 0 10px 0;

      &:hover {
        text-decoration: underline;
      }
    }

    .signin-button {
      display: inline-block;
      width: 100%;
      padding: 12px;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      background-color: black;
      border: none;
      border-radius: 14px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #2c2c2c;
      }
    }

    .sigin-form-signup-link {
      margin-top: 15px;
      font-size: 12px;
      color: #666;

      span {
        color: var(--black, #1e1e1e);
        font-family: Poppins;
        font-size: 13px;
        font-weight: 400;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 20px; // Adjust padding for smaller screens
    .signin-form {
      max-width: 320px;
    }
    .signin-title {
      font-size: 20px;
    }

    .signin-subtitle {
      font-size: 14px;
    }

    .form-input {
      height: 50px;
    }
  }
}