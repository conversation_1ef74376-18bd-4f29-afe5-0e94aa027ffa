import React, { useState } from "react";
import emailjs from "@emailjs/browser";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const Feedback = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    feedback: "",
  });

  const [formErrors, setFormErrors] = useState({
    name: "",
    email: "",
    feedback: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));

    setFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    const requiredFields = ["name", "email", "feedback"];
    const emptyFields = requiredFields.filter((field) => !formData[field]);

    if (emptyFields.length > 0) {
      toast.error("Please fill all required fields");
      return;
    }
    setLoading(true);
    emailjs
      .send(
        "service_vxjm0z4",
        "template_6a0b8hk",
        {
          from_name: formData.name,
          message: formData.feedback,
          email_id: formData.email,
        },
        "nFjOw40UmTZoXvsq-"
      )
      .then(
        () => {
          toast.success("Feedback submitted successfully");
          setFormData({
            name: "",
            email: "",
            feedback: "",
          });
          navigate("/");
        },
        () => {
          toast.error("Failed to submit feedback");
        }
      )
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="relative  bg-white shadow-lg rounded-lg p-6 w-full max-w-md">
      <span onClick={() => navigate(-1)} className="cursor-pointer p-3 top-0 right-0 absolute h-10  text-3xl">
        <img src="assets/Cross.svg" alt="" className="w-5" />
      </span>
        <h2 className="text-2xl font-bold mb-4 text-center">
          We value your feedback!
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700"
            >
              Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter your name"
              className=" block w-full p-2.5 rounded-md border-gray-300 sm:text-sm"
            />
            {formErrors.name && (
              <p className="text-red-500 text-sm">{formErrors.name}</p>
            )}
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              className=" p-2.5 block w-full rounded-md border-gray-300 "
            />
            {formErrors.email && (
              <p className="text-red-500 text-sm">{formErrors.email}</p>
            )}
          </div>

          <div>
            <label
              htmlFor="feedback"
              className="block text-sm font-medium text-gray-700"
            >
              Feedback
            </label>
            <textarea
              id="feedback"
              name="feedback"
              value={formData.feedback}
              onChange={handleChange}
              placeholder="Share your thoughts with us"
              rows="4"
              className="p-2.5 block w-full rounded-md border-gray-300 "
            />
            {formErrors.feedback && (
              <p className="text-red-500 text-sm">{formErrors.feedback}</p>
            )}
          </div>

          <button
            type="submit"
            className="w-full bg-black text-white py-2 px-4 rounded-md shadow hover:bg-gray-600 focus:outline-none"
          >
            {loading ? "submitting..." : "Submit Feedback"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Feedback;
