import React, { useState, useContext } from "react";
import Header from "../components/header";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import axios from "axios";
import secureLocalStorage from "react-secure-storage";
import { AppContext } from "../Context/AppContext";

const VerifyOtp = () => {
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const navigate = useNavigate();
  const location = useLocation();
  const { getUserData } = useContext(AppContext);

  const validate = (e) => {
    e.preventDefault();
    if (!Boolean(code)) {
      setError("Validation code is required");
      return;
    } else {
      handleSubmit();
    }
  };

  const handleSubmit = () => {
    const formDataPayload = new FormData();
    formDataPayload.append("email", location?.state?.formData?.email);
    formDataPayload.append("otp", Number(code));
    setLoading(true);

    if (location?.state?.fromRoute1) {
      // Handle OTP verification for signup
      axios
        .post(
          `${import.meta.env.VITE_API_URL}/auth/verify-otp`,
          formDataPayload,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .then((res) => {
          toast.success(res?.data?.message);
          setCode("");

          // Automatically sign in after successful OTP verification
          const loginFormData = new FormData();
          loginFormData.append("email", location.state.formData.email);
          loginFormData.append("password", location.state.formData.password);

          axios
            .post(`${import.meta.env.VITE_API_URL}/auth/login`, loginFormData, {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            })
            .then((loginRes) => {
              secureLocalStorage.setItem("userData", loginRes?.data?.data);
              secureLocalStorage.setItem("authToken", loginRes?.data?.data?.access_token);
              getUserData();

              // Only show investment modal for new signups
              if (location?.state?.fromRoute1) {
                secureLocalStorage.setItem("showInvestmentModal", "true");
              }

              toast.success("Account created and logged in successfully!");
              navigate("/"); // Redirect to the main page
            })
            .catch((loginErr) => {
              console.error("Automatic login failed:", loginErr);
              toast.error(loginErr?.response?.data?.message || "Automatic login failed. Please try signing in.");
              navigate("/signin"); // Redirect to signin if auto-login fails
            })
            .finally(() => {
              setLoading(false);
            });

        })
        .catch((err) => {
          // Handle OTP verification errors
          setError(err?.response?.data?.message || "Something went wrong");
          toast.error(err?.response?.data?.message || "Something went wrong");
          setLoading(false);
        });
    } else if (location?.state?.fromRoute2) {
      // Handle OTP verification for forget password
      const formDataPayload = new FormData();
      formDataPayload.append("email", location.state.email);
      formDataPayload.append("code", Number(code));

      axios
        .post(
          `${import.meta.env.VITE_API_URL}/auth/verify-recovery-code`,
          formDataPayload,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .then((res) => {
          toast.success(res?.data?.message);
          setCode("");
          const email = location.state.email;
          navigate("/reset-password", { state: { email, code } });
        })
        .catch((err) => {
          setError(err?.response?.data?.message || "Something went wrong");
          toast.error(err?.response?.data?.message || "Something went wrong");
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setError("Invalid navigation state");
      toast.error("Invalid navigation state");
      setLoading(false);
    }
  };
  return (
    <>
      <Header />

      <div className="login-main">
        <div className="login-main-illustration">
          <img src="/assets/illustration.png" alt="illustration" />
        </div>
        <div className="signin-form">
          <h2 className="signin-title">Please Enter OTP </h2>
          <p className="signin-subtitle"> Enter the OTP sent to your email </p>
          <form className="">
            <div className="my-10">
              <div className="form-group ">
                <label htmlFor="OTP" className="form-label">
                  Enter the OTP
                </label>
                <input
                  type="text"
                  id="OTP"
                  name="OTP"
                  placeholder="Enter OTP"
                  className="form-input"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                />
                {error && <p className="error-message">{error}</p>}
              </div>
              {/* <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Enter your Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Password"
                    className="form-input"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? (
                      <img src="/assets/eye.svg" />
                    ) : (
                      <img src="/assets/eye-close.svg" />
                    )}
                  </button>
                </div>
              </div> */}
            </div>
            <div className="form-group-links">
              {/* <p className="forgot-password">Forgot Password?</p> */}

              <button
                type="submit"
                onClick={validate}
                disabled={loading}
                className="signin-button"
              >
                {loading ? "Verifying..." : "Verify OTP"}
              </button>
              <p className="sigin-form-signup-link">
                go to sign in{" "}
                <span
                  onClick={() => navigate("/signin")}
                  className="cursor-pointer"
                >
                  Sign in
                </span>
              </p>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default VerifyOtp;
