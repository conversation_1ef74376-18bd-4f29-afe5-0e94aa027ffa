import React, { useState } from "react";
import Header from "../components/header";
import { useNavigate, useLocation } from "react-router-dom";
import axios from "axios";
import { toast } from "react-toastify";

const ResetPassword = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showPassword, setShowPassword] = useState(false);
  const [showPassword2, setShowPassword2] = useState(false);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    password: "",
    rePassword: "",
  });

  const [formErrors, setFormErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const validate = (values) => {
    const errors = {};
    const passwordRegex =
      /^(?=.*[!@#$%^&*(),.?":{}|<>])(?=.*[a-zA-Z])(?=.*[0-9]).{8,}$/;

    if (values.password === "") {
      errors.password = "Password is required";
    } else if (!passwordRegex.test(values.password)) {
      errors.password =
        "Password must be at least 8 characters long and contain a mix of letters, numbers, and symbols";
    }

    if (values.rePassword === "") {
      errors.rePassword = "Re-enter Password is required";
    } else if (values.password !== values.rePassword) {
      errors.rePassword = "Passwords do not match. Please try again.";
    }

    return errors;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const errors = validate(formData);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      Object.keys(errors).forEach((key) => {
        toast.error(errors[key]);
      });
      return;
    }
    const formDataPayload = new FormData();
    formDataPayload.append("email", location.state.email);
    formDataPayload.append("new_password", formData.password);
    formDataPayload.append("confirm_password", formData.rePassword);

    setLoading(true);
    axios
      .post(
        `${import.meta.env.VITE_API_URL}/auth/reset-password`,
        formDataPayload,
        {
          headers: {
            "Content-Type": "multipart/formData",
          },
        }
      )
      .then(() => {
        toast.success("Password reset successfully!");
        navigate("/signin");
      })
      .catch((err) => {
        toast.error(err?.response?.data?.message || "Something went wrong");
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <Header />

      <div className="login-main">
        <div className="login-main-illustration">
          <img src="/assets/illustration.png" alt="illustration" />
        </div>
        <div className="signin-form">
          <h2 className="signin-title">Create a New Password</h2>
          {/* <p className="signin-subtitle">
            Login to continue your conversations <br />with AI.
          </p> */}
          <form onSubmit={handleSubmit} className="">
            <div className="my-10">
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Enter your new Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder="Password"
                    className="form-input"
                    value={formData.password}
                    onChange={handleChange}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    <img
                      src={
                        showPassword
                          ? "/assets/eye.svg"
                          : "/assets/eye-close.svg"
                      }
                      alt="toggle visibility"
                    />
                  </button>
                </div>
                {formErrors.password && (
                  <p className="error-message">{formErrors.password}</p>
                )}
              </div>
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  Re-Enter your Password
                </label>
                <div className="password-wrapper">
                  <input
                    type={showPassword2 ? "text" : "password"}
                    id="rePassword"
                    name="rePassword"
                    placeholder="Re-enter Password"
                    className="form-input"
                    value={formData.rePassword}
                    onChange={handleChange}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword2(!showPassword2)}
                    className="password-toggle"
                  >
                    <img
                      src={
                        showPassword2
                          ? "/assets/eye.svg"
                          : "/assets/eye-close.svg"
                      }
                      alt="toggle visibility"
                    />
                  </button>
                </div>
                {formErrors.rePassword && (
                  <p className="error-message">{formErrors.rePassword}</p>
                )}
              </div>
            </div>
            <div className="form-group-links">
              {/* <p
                className="forgot-password"
                onClick={() => navigate("/verify-forget-password")}
              >
                Forgot Password?
              </p> */}

              <button
                type="submit"
                className="signin-button"
                disabled={loading}
              >
                {loading ? "Loading..." : "Continue"}
              </button>
              <p className="sigin-form-signup-link">
                go to sign in{" "}
                <span
                  onClick={() => navigate("/signin")}
                  className="cursor-pointer"
                >
                  Sign in
                </span>
              </p>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ResetPassword;
