import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Login from "./registration/login";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";
import SignUp from "./registration/signup";
import Chat from "./components/chat";
import Layout from "./components/layout";
import VerifyEmail from "./registration/verifyEmail";
import VerifyOtp from "./registration/verifyOtp";
import ResetPassword from "./registration/resetPassword";
import AboutUs from "./registration/aboutUs";
import Feedback from "./components/feedback";
import GuestRoute from "./GuestRoute"; 
///////////////dep 

function App() {
  return (
    <>
      <ToastContainer />
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route
            path="/signin"
            element={
              <GuestRoute>
                <Login />
              </GuestRoute>
            }
          />
          <Route
            path="/signup"
            element={
              <GuestRoute>
                <SignUp />
              </GuestRoute>
            }
          />
          <Route
            path="/verify-forget-password"
            element={
              <GuestRoute>
                <VerifyEmail />
              </GuestRoute>
            }
          />
          <Route
            path="/verify-otp"
            element={
              <GuestRoute>
                <VerifyOtp />
              </GuestRoute>
            }
          />
          <Route
            path="/reset-password"
            element={
              <GuestRoute>
                <ResetPassword />
              </GuestRoute>
            }
          />
          <Route path="about-us" element={<AboutUs />} />

          <Route path="/" element={<Layout />}>
            <Route index element={<Chat />} />
            <Route path="feedback" element={<Feedback />} />
          </Route>
        </Routes>
      </Router>
    </>
  );
}

export default App;
