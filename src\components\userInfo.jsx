import React, { useContext, useState, useEffect } from "react";
import axios from "axios";
import secureLocalStorage from "react-secure-storage";
import { toast } from "react-toastify";
import { AppContext } from "../Context/AppContext";

const InvestmentModal = ({ showModal, onClose, setShowModal }) => {
  const [form, setForm] = useState({
    annualIncome: "",
    riskProfile: "",
    investmentFrequency: "",
    customFrequency: "",
    investmentAmount: "",
    isStudent: false, // Added for student checkbox
  });
  const {
    getUserData,
    socketRef,
    setChatHistory,
    setIsResponseStreaming,
    stopAIResponse,
    profileData,
    isMemeMode,
    sendMemeMessage
  } = useContext(AppContext);

  // Load saved investment data when modal is opened
  useEffect(() => {
    if (showModal) {
      const savedInvestment = secureLocalStorage.getItem("userInvestment");
      console.log("Saved investment data:", savedInvestment);

      if (savedInvestment && savedInvestment.data) {
        const investmentData = savedInvestment.data;
        console.log("Investment data to load:", investmentData);

        // Check if annual income is "Student"
        const isStudent = investmentData.annual_income === "Student";

        // Determine if frequency is a standard option or custom
        const standardFrequencies = ["Daily", "Weekly", "Monthly", "Yearly"];
        const isCustomFrequency = investmentData.frequency && !standardFrequencies.includes(investmentData.frequency);

        // Set form data with saved values
        const newFormData = {
          annualIncome: isStudent ? "" : investmentData.annual_income || "",
          riskProfile: investmentData.risk_profile || "",
          investmentFrequency: isCustomFrequency ? "Custom" : investmentData.frequency || "",
          customFrequency: isCustomFrequency ? investmentData.frequency : "",
          investmentAmount: investmentData.investment || "",
          isStudent: isStudent,
        };

        console.log("Setting form data to:", newFormData);
        setForm(newFormData);
      } else {
        console.log("No saved investment data found or data is invalid");
      }
    }
  }, [showModal]);

  const handleFormSubmit = async () => {
    const userData = secureLocalStorage.getItem("userData");
    if (!userData?.email) {
      console.error("No email found in secure local storage.");
      return;
    }
    try {
      const formData = new FormData();
      formData.append("email", userData.email);
      formData.append(
        "annual_income",
        form.isStudent ? "Student" : form.annualIncome
      );
      formData.append("risk_profile", form.riskProfile);
      formData.append("frequency", form.investmentFrequency);
      formData.append("investment", form.investmentAmount);

      if (form.investmentFrequency === "Custom") {
        formData.append("frequency", form.customFrequency);
      }

      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/user_profile/save_user_info`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const userInfo = response.data;
      if (userInfo) {
        // Save the investment data in secure storage
        secureLocalStorage.setItem("userInvestment", userInfo);
        // Remove the flag that triggers the modal
        secureLocalStorage.removeItem("showInvestmentModal");
        // Set the flag to indicate user has seen the modal
        secureLocalStorage.setItem("hasSeenInvestmentModal", "true");

        // Create a session if one doesn't exist
        if (!secureLocalStorage.getItem("session_id")) {
          try {
            const sessionResponse = await axios.get(
              `${import.meta.env.VITE_API_URL}/chatbot/create-session`
            );
            const newSessionId = sessionResponse.data.session_id;
            secureLocalStorage.setItem("session_id", newSessionId);

            // Need to reconnect the WebSocket with the new session ID
            if (socketRef.current) {
              socketRef.current.close();
            }
            socketRef.current = new WebSocket(
              `wss://cryptopmentor-backend.onrender.com/chatbot/ai-response/${newSessionId}`
            );

            // Wait for the WebSocket to open before sending a message
            socketRef.current.onopen = () => {
              sendWelcomeMessage(userInfo.data);
            };

            socketRef.current.onmessage = (event) => {
              const messageData = JSON.parse(event.data);
              const botResponse = messageData.data.v;

              if (typeof botResponse === "string" && botResponse.trim() !== "") {
                const cleanResponse = botResponse;

                if (cleanResponse === "__END_OF_RESPONSE__") {
                  setIsResponseStreaming(false);
                } else {
                  setIsResponseStreaming(true);
                  setChatHistory((prev) => {
                    const updatedChatHistory = [...prev];
                    const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];

                    updatedChatHistory[updatedChatHistory.length - 1] = {
                      ...lastMessage,
                      answer: lastMessage.answer + " " + cleanResponse,
                    };

                    return updatedChatHistory;
                  });
                }
              }
            };

            socketRef.current.onerror = (error) => {
              console.error("WebSocket error:", error);
            };
          } catch (error) {
            console.error("Error creating session:", error);
          }
        } else {
          // If session already exists, just send the welcome message
          sendWelcomeMessage(userInfo.data);
        }
      }

      toast.success(
        response?.data?.data?.message || "Thank you for providing info!"
      );
      getUserData();
      setShowModal(false);
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong");
      console.error("Error fetching user data:", error);
    }
  };

  // Function to send an automatic welcome message after investment details are submitted
  const sendWelcomeMessage = async (userInfoData) => {
    // Create a welcome message that will appear as if the user sent it
    const welcomeMessage = {
      question: "Hi, I've just completed my investment profile. Can you provide me with some personalized crypto investment advice based on my profile?",
      answer: "",
      userAvatar: "/assets/User Avatar.svg",
      aiAvatar: "/assets/AI Avatar.svg",
    };

    // Add the message to chat history
    setChatHistory((prev) => [...prev, welcomeMessage]);
    setIsResponseStreaming(true);

    // Use meme mode API if meme mode is enabled
    if (isMemeMode) {
      try {
        const memeResponse = await sendMemeMessage(welcomeMessage.question, userInfoData);

        // Update the chat history with the meme response
        setChatHistory((prev) => {
          const updatedChatHistory = [...prev];
          const lastMessage = updatedChatHistory[updatedChatHistory.length - 1];

          updatedChatHistory[updatedChatHistory.length - 1] = {
            ...lastMessage,
            answer: memeResponse.analysis || "🎉 Welcome to meme mode! Let's get you some epic crypto advice! 🚀"
          };

          return updatedChatHistory;
        });

        setIsResponseStreaming(false);
      } catch (error) {
        console.error('Meme mode API error:', error);
        // Fallback to regular WebSocket if meme API fails
        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
          socketRef.current.send(
            JSON.stringify({
              query: welcomeMessage.question,
              user_info: userInfoData
            })
          );
        }
      }
    } else {
      // Use regular WebSocket for normal mode
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.send(
          JSON.stringify({
            query: welcomeMessage.question,
            user_info: userInfoData
          })
        );
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm({
      ...form,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  if (!showModal) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md">
        <h2 className="text-center text-lg font-semibold mb-4">
          Investment Details
        </h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Are you a student?
            </label>
            <input
              type="checkbox"
              name="isStudent"
              checked={form.isStudent}
              onChange={handleInputChange}
              className="mr-2"
            />
            <span>Yes, I am a student</span>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              What is your annual income?
            </label>
            <input
              type="number"
              name="annualIncome"
              value={form.annualIncome}
              onChange={handleInputChange}
              disabled={form.isStudent}
              placeholder={form.isStudent ? "Student" : "Enter your annual income"}
              className="w-full border rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              What is your risk profile?
            </label>
            <select
              name="riskProfile"
              value={form.riskProfile}
              onChange={handleInputChange}
              className="w-full border rounded-md p-2"
            >
              <option value="">Select</option>
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              How frequently are you looking to invest?
            </label>
            <select
              name="investmentFrequency"
              value={form.investmentFrequency}
              onChange={handleInputChange}
              className="w-full border rounded-md p-2"
            >
              <option value="">Select</option>
              <option value="Daily">Daily</option>
              <option value="Weekly">Weekly</option>
              <option value="Monthly">Monthly</option>
              <option value="Yearly">Yearly</option>
              <option value="Custom">Custom</option>
            </select>
            {form.investmentFrequency === "Custom" && (
              <input
                type="text"
                name="customFrequency"
                value={form.customFrequency}
                onChange={handleInputChange}
                placeholder="Specify custom frequency"
                className="w-full border rounded-md p-2 mt-2"
              />
            )}
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              How much money are you planning on investing? (Estimated Amount)
            </label>
            <input
              type="number"
              name="investmentAmount"
              value={form.investmentAmount}
              onChange={handleInputChange}
              placeholder="Enter amount"
              className="w-full border rounded-md p-2"
            />
          </div>
        </div>
        <div className="mt-6 flex justify-between">
          <button
            className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition"
            onClick={() => {
              // Reset form when closing the modal
              setForm({
                annualIncome: "",
                riskProfile: "",
                investmentFrequency: "",
                customFrequency: "",
                investmentAmount: "",
                isStudent: false,
              });
              setShowModal(false);
            }}
          >
            Cancel
          </button>
          <button
            className="bg-black text-white py-2 px-4 rounded-md hover:bg-gray-600 transition"
            onClick={handleFormSubmit}
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default InvestmentModal;
