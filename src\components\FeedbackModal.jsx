import React, { useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { Icon } from "@iconify/react";

const FeedbackModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    wowMoment: '',
    shareScore: 5,
    shareReason: '',
    missingForInvestment: '',
    confusionPoints: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('https://memecoin-backend-ym1s.onrender.com/api/feedback', formData);
      toast.success('Feedback submitted successfully!');
      onClose();
    } catch (error) {
      toast.error('Failed to submit feedback. Please try again.');
      console.error('Feedback submission error:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">Your Feedback Matters!</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <Icon icon="mdi:close" className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              What made you go "wow"?
            </label>
            <textarea
              name="wowMoment"
              value={formData.wowMoment}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-200 focus:border-pink-200"
              rows="3"
              placeholder="Was there anything that stood out as impressive, helpful, or exciting?"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Would you share this with a friend?
            </label>
            <div className="flex items-center space-x-4 mb-2">
              <span className="text-sm text-gray-500">1 = Definitely not</span>
              <input
                type="range"
                name="shareScore"
                min="1"
                max="10"
                value={formData.shareScore}
                onChange={handleChange}
                className="w-full"
              />
              <span className="text-sm text-gray-500">10 = Absolutely</span>
            </div>
            <div className="text-center text-sm font-medium">
              Score: {formData.shareScore}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Why or why not?
            </label>
            <textarea
              name="shareReason"
              value={formData.shareReason}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-200 focus:border-pink-200"
              rows="2"
              placeholder="Share your thoughts..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              What's missing for you to actually invest?
            </label>
            <textarea
              name="missingForInvestment"
              value={formData.missingForInvestment}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-200 focus:border-pink-200"
              rows="3"
              placeholder="What would give you enough confidence or motivation to take action?"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Did anything confuse you?
            </label>
            <textarea
              name="confusionPoints"
              value={formData.confusionPoints}
              onChange={handleChange}
              className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-200 focus:border-pink-200"
              rows="2"
              placeholder="Any part of the experience that felt unclear or frustrating?"
            />
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Skip
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-black text-white rounded-full hover:bg-gray-800 transition-colors"
            >
              Submit Feedback
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FeedbackModal; 